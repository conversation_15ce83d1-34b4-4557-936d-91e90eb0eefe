exports.id=863,exports.ids=[863],exports.modules={4780:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(49384),a=r(82348);function n(...e){return(0,a.QP)((0,s.$)(e))}},14246:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>f});var s=r(60687),a=r(85814),n=r.n(a),i=r(16189),o=r(43210),l=r(11860),c=r(12941),d=r(21134),m=r(363),h=r(29523),x=r(96871),p=r(4780);let v=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Expertise",href:"/expertise"},{name:"Projects",href:"/projects"},{name:"Contact",href:"/contact"}];function f(){let e=(0,i.usePathname)(),{theme:t,toggleTheme:r}=(0,x.D)(),[a,f]=(0,o.useState)(!1);return(0,s.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b border-secondary/20 bg-surface/80 backdrop-blur-md",children:[(0,s.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Global",children:[(0,s.jsx)("div",{className:"flex lg:flex-1",children:(0,s.jsx)(n(),{href:"/",className:"-m-1.5 p-1.5",children:(0,s.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent",children:"Metamorphic Labs"})})}),(0,s.jsx)("div",{className:"flex lg:hidden",children:(0,s.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:()=>f(!a),className:"text-text-primary",children:[(0,s.jsx)("span",{className:"sr-only",children:"Open main menu"}),a?(0,s.jsx)(l.A,{className:"h-6 w-6","aria-hidden":"true"}):(0,s.jsx)(c.A,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,s.jsx)("div",{className:"hidden lg:flex lg:gap-x-12",children:v.map(t=>(0,s.jsx)(n(),{href:t.href,className:(0,p.cn)("text-sm font-medium transition-colors hover:text-secondary",e===t.href?"text-secondary border-b-2 border-secondary":"text-text-primary hover:text-secondary"),children:t.name},t.name))}),(0,s.jsxs)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4",children:[(0,s.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:r,className:"text-text-primary hover:text-secondary",children:[(0,s.jsx)("span",{className:"sr-only",children:"Toggle theme"}),"dark"===t?(0,s.jsx)(d.A,{className:"h-5 w-5"}):(0,s.jsx)(m.A,{className:"h-5 w-5"})]}),(0,s.jsx)(h.$,{asChild:!0,className:"bg-accent text-surface hover:bg-accent/90",children:(0,s.jsx)(n(),{href:"/contact",children:"Get Started"})})]})]}),a&&(0,s.jsxs)("div",{className:"lg:hidden",children:[(0,s.jsx)("div",{className:"fixed inset-0 z-50"}),(0,s.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-surface px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-secondary/20",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)(n(),{href:"/",className:"-m-1.5 p-1.5",onClick:()=>f(!1),children:(0,s.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent",children:"Metamorphic Labs"})}),(0,s.jsxs)(h.$,{variant:"ghost",size:"sm",onClick:()=>f(!1),className:"text-text-primary",children:[(0,s.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,s.jsx)(l.A,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,s.jsx)("div",{className:"mt-6 flow-root",children:(0,s.jsxs)("div",{className:"-my-6 divide-y divide-secondary/20",children:[(0,s.jsx)("div",{className:"space-y-2 py-6",children:v.map(t=>(0,s.jsx)(n(),{href:t.href,onClick:()=>f(!1),className:(0,p.cn)("-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors",e===t.href?"bg-secondary/10 text-secondary":"text-text-primary hover:bg-secondary/5 hover:text-secondary"),children:t.name},t.name))}),(0,s.jsxs)("div",{className:"py-6 space-y-4",children:[(0,s.jsx)(h.$,{variant:"ghost",onClick:r,className:"w-full justify-start text-text-primary hover:text-secondary",children:"dark"===t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.A,{className:"mr-2 h-5 w-5"}),"Light Mode"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(m.A,{className:"mr-2 h-5 w-5"}),"Dark Mode"]})}),(0,s.jsx)(h.$,{asChild:!0,className:"w-full bg-accent text-surface hover:bg-accent/90",children:(0,s.jsx)(n(),{href:"/contact",onClick:()=>f(!1),children:"Get Started"})})]})]})})]})]})]})}},17903:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},21075:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>b,metadata:()=>g});var s=r(37413),a=r(22376),n=r.n(a),i=r(68726),o=r.n(i);r(61135);var l=r(83701),c=r(64544),d=r(4536),m=r.n(d),h=r(92715),x=r(96262),p=r(63353),v=r(60343);let f={main:[{name:"About",href:"/about"},{name:"Expertise",href:"/expertise"},{name:"Projects",href:"/projects"},{name:"Contact",href:"/contact"}],legal:[{name:"Privacy Policy",href:"/legal#privacy"},{name:"Terms of Service",href:"/legal#terms"}],social:[{name:"GitHub",href:"https://github.com/metamorphic-labs",icon:h.A},{name:"LinkedIn",href:"https://linkedin.com/company/metamorphic-labs",icon:x.A},{name:"Twitter",href:"https://twitter.com/metamorphiclabs",icon:p.A},{name:"Email",href:"mailto:<EMAIL>",icon:v.A}]};function u(){return(0,s.jsx)("footer",{className:"bg-surface border-t border-secondary/20",children:(0,s.jsxs)("div",{className:"mx-auto max-w-7xl overflow-hidden px-6 py-20 sm:py-24 lg:px-8",children:[(0,s.jsx)("nav",{className:"-mb-6 columns-2 sm:flex sm:justify-center sm:space-x-12","aria-label":"Footer",children:f.main.map(e=>(0,s.jsx)("div",{className:"pb-6",children:(0,s.jsx)(m(),{href:e.href,className:"text-sm leading-6 text-text-secondary hover:text-secondary transition-colors",children:e.name})},e.name))}),(0,s.jsx)("div",{className:"mt-10 flex justify-center space-x-10",children:f.social.map(e=>(0,s.jsxs)(m(),{href:e.href,className:"text-text-secondary hover:text-secondary transition-colors",target:"_blank",rel:"noopener noreferrer",children:[(0,s.jsx)("span",{className:"sr-only",children:e.name}),(0,s.jsx)(e.icon,{className:"h-6 w-6","aria-hidden":"true"})]},e.name))}),(0,s.jsx)("div",{className:"mt-10 flex justify-center space-x-4 text-xs leading-5 text-text-secondary",children:f.legal.map((e,t)=>(0,s.jsxs)("span",{children:[t>0&&(0,s.jsx)("span",{className:"mx-2",children:"•"}),(0,s.jsx)(m(),{href:e.href,className:"hover:text-secondary transition-colors",children:e.name})]},e.name))}),(0,s.jsxs)("p",{className:"mt-10 text-center text-xs leading-5 text-text-secondary",children:["\xa9 ",new Date().getFullYear()," Metamorphic Labs LLC. All rights reserved."]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsx)("p",{className:"text-xs text-text-secondary",children:"Transforming ideas into intelligent solutions through adaptive AI technology."})})]})})}let g={title:"Metamorphic Labs | AI-Powered Software Solutions",description:"Transforming ideas into intelligent solutions through adaptive AI technology. Specializing in AI platforms, intelligent automation, and custom software development.",keywords:"AI, machine learning, software development, automation, custom software, AI platforms",authors:[{name:"Metamorphic Labs LLC"}],creator:"Metamorphic Labs LLC",publisher:"Metamorphic Labs LLC",openGraph:{type:"website",locale:"en_US",url:"https://metamorphiclabs.com",title:"Metamorphic Labs | AI-Powered Software Solutions",description:"Transforming ideas into intelligent solutions through adaptive AI technology.",siteName:"Metamorphic Labs"},twitter:{card:"summary_large_image",title:"Metamorphic Labs | AI-Powered Software Solutions",description:"Transforming ideas into intelligent solutions through adaptive AI technology.",creator:"@metamorphiclabs"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function b({children:e}){return(0,s.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,s.jsx)("body",{className:`${n().variable} ${o().variable} min-h-screen bg-surface text-text-primary antialiased`,children:(0,s.jsx)(l.ThemeProvider,{defaultTheme:"dark",storageKey:"metamorphic-theme",children:(0,s.jsxs)("div",{className:"relative flex min-h-screen flex-col",children:[(0,s.jsx)(c.Navigation,{}),(0,s.jsx)("main",{className:"flex-1",children:e}),(0,s.jsx)(u,{})]})})})})}},29523:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var s=r(60687);r(43210);var a=r(81391),n=r(24224),i=r(4780);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l({className:e,variant:t,size:r,asChild:n=!1,...l}){let c=n?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...l})}},58115:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23)),Promise.resolve().then(r.bind(r,14246)),Promise.resolve().then(r.bind(r,96871))},61135:()=>{},64544:(e,t,r)=>{"use strict";r.d(t,{Navigation:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call Navigation() from the server but Navigation is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx","Navigation")},64759:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},82091:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23)),Promise.resolve().then(r.bind(r,64544)),Promise.resolve().then(r.bind(r,83701))},83701:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>a});var s=r(12907);let a=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx","ThemeProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx","useTheme")},96871:(e,t,r)=>{"use strict";r.d(t,{D:()=>o,ThemeProvider:()=>i});var s=r(60687),a=r(43210);let n=(0,a.createContext)({theme:"dark",setTheme:()=>null,toggleTheme:()=>null});function i({children:e,defaultTheme:t="dark",storageKey:r="metamorphic-theme",...i}){let[o,l]=(0,a.useState)(t);return(0,s.jsx)(n.Provider,{...i,value:{theme:o,setTheme:e=>{localStorage.setItem(r,e),l(e)},toggleTheme:()=>{let e="dark"===o?"light":"dark";localStorage.setItem(r,e),l(e)}},children:e})}let o=()=>{let e=(0,a.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}}};