'use client'

import { useMemo } from 'react'
import ReactMarkdown from 'react-markdown'
import { Prism as <PERSON>ynta<PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-syntax-highlighter'
import { oneDark } from 'react-syntax-highlighter/dist/esm/styles/prism'

interface MDXContentProps {
  content: string
}

// eslint-disable-next-line @typescript-eslint/no-explicit-any
type ComponentProps = any

export function MDXContent({ content }: MDXContentProps) {
  const components = useMemo(() => ({
    h1: ({ children }: ComponentProps) => (
      <h1 className="text-3xl font-bold text-text-primary mb-6 mt-8 first:mt-0">
        {children}
      </h1>
    ),
    h2: ({ children }: ComponentProps) => (
      <h2 className="text-2xl font-semibold text-text-primary mb-4 mt-8 first:mt-0">
        {children}
      </h2>
    ),
    h3: ({ children }: ComponentProps) => (
      <h3 className="text-xl font-semibold text-text-primary mb-3 mt-6">
        {children}
      </h3>
    ),
    h4: ({ children }: ComponentProps) => (
      <h4 className="text-lg font-semibold text-text-primary mb-2 mt-4">
        {children}
      </h4>
    ),
    p: ({ children }: ComponentProps) => (
      <p className="text-text-secondary leading-7 mb-4">
        {children}
      </p>
    ),
    ul: ({ children }: ComponentProps) => (
      <ul className="list-disc list-inside text-text-secondary mb-4 space-y-2">
        {children}
      </ul>
    ),
    ol: ({ children }: ComponentProps) => (
      <ol className="list-decimal list-inside text-text-secondary mb-4 space-y-2">
        {children}
      </ol>
    ),
    li: ({ children }: ComponentProps) => (
      <li className="leading-7">
        {children}
      </li>
    ),
    strong: ({ children }: ComponentProps) => (
      <strong className="font-semibold text-text-primary">
        {children}
      </strong>
    ),
    em: ({ children }: ComponentProps) => (
      <em className="italic text-text-primary">
        {children}
      </em>
    ),
    code: ({ inline, className, children, ...props }: ComponentProps) => {
      const match = /language-(\w+)/.exec(className || '')
      return !inline && match ? (
        <div className="my-6">
          <SyntaxHighlighter
            style={oneDark}
            language={match[1]}
            PreTag="div"
            className="rounded-lg"
            {...props}
          >
            {String(children).replace(/\n$/, '')}
          </SyntaxHighlighter>
        </div>
      ) : (
        <code className="bg-tertiary/10 text-tertiary px-1.5 py-0.5 rounded text-sm font-mono" {...props}>
          {children}
        </code>
      )
    },
    blockquote: ({ children }: ComponentProps) => (
      <blockquote className="border-l-4 border-secondary pl-4 my-6 italic text-text-secondary">
        {children}
      </blockquote>
    ),
    a: ({ href, children }: ComponentProps) => (
      <a
        href={href}
        className="text-secondary hover:text-secondary/80 underline underline-offset-2 transition-colors"
        target={href?.startsWith('http') ? '_blank' : undefined}
        rel={href?.startsWith('http') ? 'noopener noreferrer' : undefined}
      >
        {children}
      </a>
    ),
    hr: () => (
      <hr className="border-secondary/20 my-8" />
    ),
    table: ({ children }: ComponentProps) => (
      <div className="overflow-x-auto my-6">
        <table className="min-w-full border border-secondary/20 rounded-lg">
          {children}
        </table>
      </div>
    ),
    thead: ({ children }: ComponentProps) => (
      <thead className="bg-secondary/5">
        {children}
      </thead>
    ),
    th: ({ children }: ComponentProps) => (
      <th className="px-4 py-2 text-left font-semibold text-text-primary border-b border-secondary/20">
        {children}
      </th>
    ),
    td: ({ children }: ComponentProps) => (
      <td className="px-4 py-2 text-text-secondary border-b border-secondary/10">
        {children}
      </td>
    ),
  }), [])

  return (
    <div className="prose prose-lg max-w-none">
      <ReactMarkdown components={components}>
        {content}
      </ReactMarkdown>
    </div>
  )
}
