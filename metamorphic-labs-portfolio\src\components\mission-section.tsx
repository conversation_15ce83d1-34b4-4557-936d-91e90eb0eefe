'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/card'

export function MissionSection() {
  return (
    <section className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            viewport={{ once: true }}
            className="text-center mb-16"
          >
            <h2 className="text-3xl font-bold tracking-tight text-text-primary sm:text-4xl mb-6">
              Our Mission
            </h2>
            <p className="text-lg text-text-secondary max-w-3xl mx-auto">
              To democratize access to advanced AI technology by creating adaptive, intelligent solutions 
              that evolve with your business needs and drive meaningful transformation.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              viewport={{ once: true }}
            >
              <Card className="bg-surface-alt border-secondary/20 h-full">
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold text-text-primary mb-4">
                    What We Believe
                  </h3>
                  <div className="space-y-4 text-text-secondary">
                    <p>
                      <strong className="text-text-primary">AI should be accessible:</strong> Complex 
                      technology shouldn&apos;t require a PhD to implement. We make advanced AI solutions 
                      approachable and practical for businesses of all sizes.
                    </p>
                    <p>
                      <strong className="text-text-primary">Solutions should evolve:</strong> Static 
                      systems become obsolete. Our adaptive platforms learn, grow, and improve 
                      alongside your business.
                    </p>
                    <p>
                      <strong className="text-text-primary">Innovation drives progress:</strong> We 
                      don&apos;t just follow trends—we create them. Our research-driven approach ensures 
                      you&apos;re always ahead of the curve.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, x: 20 }}
              whileInView={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              viewport={{ once: true }}
            >
              <Card className="bg-surface-alt border-secondary/20 h-full">
                <CardContent className="p-8">
                  <h3 className="text-xl font-semibold text-text-primary mb-4">
                    How We Work
                  </h3>
                  <div className="space-y-4 text-text-secondary">
                    <p>
                      <strong className="text-text-primary">Research-First Approach:</strong> Every 
                      solution begins with deep research into your industry, challenges, and 
                      opportunities for AI integration.
                    </p>
                    <p>
                      <strong className="text-text-primary">Iterative Development:</strong> We build, 
                      test, learn, and improve in rapid cycles, ensuring optimal results and 
                      continuous refinement.
                    </p>
                    <p>
                      <strong className="text-text-primary">Long-term Partnership:</strong> Our 
                      relationship doesn&apos;t end at deployment. We provide ongoing support, updates, 
                      and evolution of your AI systems.
                    </p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </section>
  )
}
