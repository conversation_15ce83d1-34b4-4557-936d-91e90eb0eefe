(()=>{var e={};e.id=893,e.ids=[893],e.modules={2315:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,4536,23))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},10974:(e,t,r)=>{"use strict";r.d(t,{cn:()=>n});var s=r(75986),a=r(8974);function n(...e){return(0,a.QP)((0,s.$)(e))}},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23469:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(37413);r(61120);var a=r(70403),n=r(50662),i=r(10974);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function d({className:e,variant:t,size:r,asChild:n=!1,...d}){let c=n?a.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:t,size:r,className:e})),...d})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30084:(e,t,r)=>{"use strict";r.d(t,{E:()=>d});var s=r(37413);r(61120);var a=r(70403),n=r(50662),i=r(10974);let o=(0,n.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function d({className:e,variant:t,asChild:r=!1,...n}){let d=r?a.DX:"span";return(0,s.jsx)(d,{"data-slot":"badge",className:(0,i.cn)(o({variant:t}),e),...n})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},39727:()=>{},47990:()=>{},51906:e=>{function t(e){var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}t.keys=()=>[],t.resolve=t,t.id=51906,e.exports=t},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56621:(e,t,r)=>{"use strict";r.d(t,{JQ:()=>a,U1:()=>n});let s=(0,r(39398).createClient)("https://etqkmihipaiiodiwxqbl.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImV0cWttaWhpcGFpaW9kaXd4cWJsIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA1NDcwMTcsImV4cCI6MjA2NjEyMzAxN30.RV25cIjc6q4GbDCPZopMGjnthN2sBkJIIqKqGd7hp8s");async function a(){let{data:e,error:t}=await s.from("projects").select("*").order("created_at",{ascending:!1});return t?(console.error("Error fetching projects:",t),[]):e||[]}async function n(e){let{data:t,error:r}=await s.from("projects").select("*").eq("slug",e).single();return r?(console.error("Error fetching project:",r),null):t}},60778:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>v,metadata:()=>h});var s=r(37413),a=r(61120),n=r(4536),i=r.n(n),o=r(94014),d=r(40918);let c=(0,r(26373).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var l=r(78963),u=r(30084),x=r(23469),p=r(56621);async function m(){let e=await (0,p.JQ)();return 0===e.length?(0,s.jsx)("div",{className:"text-center py-12",children:(0,s.jsx)("p",{className:"text-text-secondary",children:"No projects found."})}):(0,s.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3",children:e.map((e,t)=>(0,s.jsxs)(l.Zp,{className:`group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10 ${t%3==0?"md:col-span-2 lg:col-span-1":""}`,children:[(0,s.jsxs)(l.aR,{className:"pb-4",children:[(0,s.jsx)("div",{className:"flex items-start justify-between",children:(0,s.jsxs)("div",{className:"flex-1",children:[(0,s.jsx)(l.ZB,{className:"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors",children:e.title}),(0,s.jsx)(l.BT,{className:"mt-2 text-text-secondary line-clamp-2",children:e.summary||"Innovative AI-powered solution designed to transform business operations."})]})}),(0,s.jsx)("div",{className:"flex items-center gap-2 mt-4",children:(0,s.jsx)(u.E,{variant:"secondary",className:`text-xs px-2 py-1 ${e.status.toLowerCase().includes("complete")||e.status.toLowerCase().includes("beta")?"bg-accent/15 text-accent border-accent/20":e.status.toLowerCase().includes("phase")?"bg-secondary/15 text-secondary border-secondary/20":"bg-tertiary/15 text-tertiary border-tertiary/20"}`,children:e.status})})]}),(0,s.jsxs)(l.Wu,{className:"pt-0",children:[e.tags&&e.tags.length>0&&(0,s.jsxs)("div",{className:"flex flex-wrap gap-2 mb-4",children:[e.tags.slice(0,3).map(e=>(0,s.jsxs)("span",{className:"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary",children:[(0,s.jsx)(o.A,{className:"mr-1 h-3 w-3"}),e]},e)),e.tags.length>3&&(0,s.jsxs)("span",{className:"inline-flex items-center rounded-full bg-text-secondary/10 px-2 py-1 text-xs font-medium text-text-secondary",children:["+",e.tags.length-3," more"]})]}),(0,s.jsxs)("div",{className:"flex items-center text-xs text-text-secondary mb-4",children:[(0,s.jsx)(d.A,{className:"mr-1 h-3 w-3"}),new Date(e.created_at).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),(0,s.jsx)(x.$,{asChild:!0,variant:"ghost",className:"w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn",children:(0,s.jsxs)(i(),{href:`/projects/${e.slug}`,children:[(0,s.jsx)("span",{children:"Learn More"}),(0,s.jsx)(c,{className:"h-4 w-4 transition-transform group-hover/btn:translate-x-1"})]})})]})]},e.slug))})}function g(){return(0,s.jsx)("div",{className:"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3",children:Array.from({length:6}).map((e,t)=>(0,s.jsxs)(l.Zp,{className:"bg-surface-alt border-secondary/20 animate-pulse",children:[(0,s.jsx)(l.aR,{className:"pb-4",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsx)("div",{className:"h-6 bg-text-secondary/20 rounded w-3/4"}),(0,s.jsx)("div",{className:"h-4 bg-text-secondary/20 rounded w-full"}),(0,s.jsx)("div",{className:"h-4 bg-text-secondary/20 rounded w-2/3"}),(0,s.jsx)("div",{className:"h-6 bg-text-secondary/20 rounded w-20"})]})}),(0,s.jsx)(l.Wu,{className:"pt-0",children:(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)("div",{className:"h-5 bg-text-secondary/20 rounded-full w-16"}),(0,s.jsx)("div",{className:"h-5 bg-text-secondary/20 rounded-full w-20"}),(0,s.jsx)("div",{className:"h-5 bg-text-secondary/20 rounded-full w-14"})]}),(0,s.jsx)("div",{className:"h-4 bg-text-secondary/20 rounded w-32"}),(0,s.jsx)("div",{className:"h-10 bg-text-secondary/20 rounded w-full"})]})})]},t))})}let h={title:"Projects | Metamorphic Labs",description:"Explore our portfolio of AI-powered solutions, intelligent automation systems, and custom software projects."};function v(){return(0,s.jsxs)("div",{className:"min-h-screen bg-surface",children:[(0,s.jsx)("section",{className:"relative py-24 sm:py-32",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,s.jsxs)("div",{className:"mx-auto max-w-4xl text-center",children:[(0,s.jsxs)("h1",{className:"text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl",children:["Our ",(0,s.jsx)("span",{className:"bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent",children:"Projects"})]}),(0,s.jsx)("p",{className:"mt-6 text-lg leading-8 text-text-secondary sm:text-xl max-w-3xl mx-auto",children:"Discover how we transform ideas into intelligent solutions. From AI platforms to automation systems, each project showcases our commitment to innovation and excellence."})]})})}),(0,s.jsx)("section",{className:"pb-24 sm:pb-32",children:(0,s.jsx)("div",{className:"mx-auto max-w-7xl px-6 lg:px-8",children:(0,s.jsx)(a.Suspense,{fallback:(0,s.jsx)(g,{}),children:(0,s.jsx)(m,{})})})})]})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},70440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(31658);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},74075:e=>{"use strict";e.exports=require("zlib")},78339:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,85814,23))},78963:(e,t,r)=>{"use strict";r.d(t,{BT:()=>d,Wu:()=>c,ZB:()=>o,Zp:()=>n,aR:()=>i});var s=r(37413);r(61120);var a=r(10974);function n({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",e),...t})}function i({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",e),...t})}function o({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",e),...t})}function d({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",e),...t})}function c({className:e,...t}){return(0,s.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",e),...t})}},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},89294:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>c});var s=r(65239),a=r(48088),n=r(88170),i=r.n(n),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let c={children:["",{children:["projects",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60778)),"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,21075)),"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,70440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,l=["C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/projects/page",pathname:"/projects",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,164,658,970,863],()=>r(89294));module.exports=s})();