1:"$Sreact.fragment"
2:I[61483,["874","static/chunks/874-a3f83d0e9b9d06ad.js","497","static/chunks/497-fe82b947d9aaba6e.js","177","static/chunks/app/layout-7fc95bff2ebc7b3d.js"],"ThemeProvider"]
3:I[46325,["874","static/chunks/874-a3f83d0e9b9d06ad.js","497","static/chunks/497-fe82b947d9aaba6e.js","177","static/chunks/app/layout-7fc95bff2ebc7b3d.js"],"Navigation"]
4:I[87555,[],""]
5:I[31295,[],""]
6:I[6874,["874","static/chunks/874-a3f83d0e9b9d06ad.js","893","static/chunks/app/projects/page-4ea8b7c5a3b47a3b.js"],""]
7:"$Sreact.suspense"
9:I[59665,[],"OutletBoundary"]
c:I[74911,[],"AsyncMetadataOutlet"]
e:I[59665,[],"ViewportBoundary"]
10:I[59665,[],"MetadataBoundary"]
12:I[26614,[],""]
:HL["/_next/static/css/03b4b7ad05d37f7d.css","style"]
0:{"P":null,"b":"VS0hn8hgZEpjZ84bsWqJ1","p":"","c":["","projects"],"i":false,"f":[[["",{"children":["projects",{"children":["__PAGE__",{}]}]},"$undefined","$undefined",true],["",["$","$1","c",{"children":[[["$","link","0",{"rel":"stylesheet","href":"/_next/static/css/03b4b7ad05d37f7d.css","precedence":"next","crossOrigin":"$undefined","nonce":"$undefined"}]],["$","html",null,{"lang":"en","suppressHydrationWarning":true,"children":["$","body",null,{"className":"__variable_5cfdac __variable_9a8899 min-h-screen bg-surface text-text-primary antialiased","children":["$","$L2",null,{"defaultTheme":"dark","storageKey":"metamorphic-theme","children":["$","div",null,{"className":"relative flex min-h-screen flex-col","children":[["$","$L3",null,{}],["$","main",null,{"className":"flex-1","children":["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":[[["$","title",null,{"children":"404: This page could not be found."}],["$","div",null,{"style":{"fontFamily":"system-ui,\"Segoe UI\",Roboto,Helvetica,Arial,sans-serif,\"Apple Color Emoji\",\"Segoe UI Emoji\"","height":"100vh","textAlign":"center","display":"flex","flexDirection":"column","alignItems":"center","justifyContent":"center"},"children":["$","div",null,{"children":[["$","style",null,{"dangerouslySetInnerHTML":{"__html":"body{color:#000;background:#fff;margin:0}.next-error-h1{border-right:1px solid rgba(0,0,0,.3)}@media (prefers-color-scheme:dark){body{color:#fff;background:#000}.next-error-h1{border-right:1px solid rgba(255,255,255,.3)}}"}}],["$","h1",null,{"className":"next-error-h1","style":{"display":"inline-block","margin":"0 20px 0 0","padding":"0 23px 0 0","fontSize":24,"fontWeight":500,"verticalAlign":"top","lineHeight":"49px"},"children":404}],["$","div",null,{"style":{"display":"inline-block"},"children":["$","h2",null,{"style":{"fontSize":14,"fontWeight":400,"lineHeight":"49px","margin":0},"children":"This page could not be found."}]}]]}]}]],[]],"forbidden":"$undefined","unauthorized":"$undefined"}]}],["$","footer",null,{"className":"bg-surface border-t border-secondary/20","children":["$","div",null,{"className":"mx-auto max-w-7xl overflow-hidden px-6 py-20 sm:py-24 lg:px-8","children":[["$","nav",null,{"className":"-mb-6 columns-2 sm:flex sm:justify-center sm:space-x-12","aria-label":"Footer","children":[["$","div","About",{"className":"pb-6","children":["$","$L6",null,{"href":"/about","className":"text-sm leading-6 text-text-secondary hover:text-secondary transition-colors","children":"About"}]}],["$","div","Expertise",{"className":"pb-6","children":["$","$L6",null,{"href":"/expertise","className":"text-sm leading-6 text-text-secondary hover:text-secondary transition-colors","children":"Expertise"}]}],["$","div","Projects",{"className":"pb-6","children":["$","$L6",null,{"href":"/projects","className":"text-sm leading-6 text-text-secondary hover:text-secondary transition-colors","children":"Projects"}]}],["$","div","Contact",{"className":"pb-6","children":["$","$L6",null,{"href":"/contact","className":"text-sm leading-6 text-text-secondary hover:text-secondary transition-colors","children":"Contact"}]}]]}],["$","div",null,{"className":"mt-10 flex justify-center space-x-10","children":[["$","$L6","GitHub",{"href":"https://github.com/metamorphic-labs","className":"text-text-secondary hover:text-secondary transition-colors","target":"_blank","rel":"noopener noreferrer","children":[["$","span",null,{"className":"sr-only","children":"GitHub"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-github h-6 w-6","aria-hidden":"true","children":[["$","path","tonef",{"d":"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4"}],["$","path","9comsn",{"d":"M9 18c-4.51 2-5-2-7-2"}],"$undefined"]}]]}],["$","$L6","LinkedIn",{"href":"https://linkedin.com/company/metamorphic-labs","className":"text-text-secondary hover:text-secondary transition-colors","target":"_blank","rel":"noopener noreferrer","children":[["$","span",null,{"className":"sr-only","children":"LinkedIn"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-linkedin h-6 w-6","aria-hidden":"true","children":[["$","path","c2jq9f",{"d":"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}],["$","rect","mk3on5",{"width":"4","height":"12","x":"2","y":"9"}],["$","circle","bt5ra8",{"cx":"4","cy":"4","r":"2"}],"$undefined"]}]]}],["$","$L6","Twitter",{"href":"https://twitter.com/metamorphiclabs","className":"text-text-secondary hover:text-secondary transition-colors","target":"_blank","rel":"noopener noreferrer","children":[["$","span",null,{"className":"sr-only","children":"Twitter"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-twitter h-6 w-6","aria-hidden":"true","children":[["$","path","pff0z6",{"d":"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"}],"$undefined"]}]]}],["$","$L6","Email",{"href":"mailto:<EMAIL>","className":"text-text-secondary hover:text-secondary transition-colors","target":"_blank","rel":"noopener noreferrer","children":[["$","span",null,{"className":"sr-only","children":"Email"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-mail h-6 w-6","aria-hidden":"true","children":[["$","path","132q7q",{"d":"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7"}],["$","rect","izxlao",{"x":"2","y":"4","width":"20","height":"16","rx":"2"}],"$undefined"]}]]}]]}],["$","div",null,{"className":"mt-10 flex justify-center space-x-4 text-xs leading-5 text-text-secondary","children":[["$","span","Privacy Policy",{"children":[false,["$","$L6",null,{"href":"/legal#privacy","className":"hover:text-secondary transition-colors","children":"Privacy Policy"}]]}],["$","span","Terms of Service",{"children":[["$","span",null,{"className":"mx-2","children":"•"}],["$","$L6",null,{"href":"/legal#terms","className":"hover:text-secondary transition-colors","children":"Terms of Service"}]]}]]}],["$","p",null,{"className":"mt-10 text-center text-xs leading-5 text-text-secondary","children":["© ",2025," Metamorphic Labs LLC. All rights reserved."]}],["$","div",null,{"className":"mt-6 text-center","children":["$","p",null,{"className":"text-xs text-text-secondary","children":"Transforming ideas into intelligent solutions through adaptive AI technology."}]}]]}]}]]}]}]}]}]]}],{"children":["projects",["$","$1","c",{"children":[null,["$","$L4",null,{"parallelRouterKey":"children","error":"$undefined","errorStyles":"$undefined","errorScripts":"$undefined","template":["$","$L5",null,{}],"templateStyles":"$undefined","templateScripts":"$undefined","notFound":"$undefined","forbidden":"$undefined","unauthorized":"$undefined"}]]}],{"children":["__PAGE__",["$","$1","c",{"children":[["$","div",null,{"className":"min-h-screen bg-surface","children":[["$","section",null,{"className":"relative py-24 sm:py-32","children":["$","div",null,{"className":"mx-auto max-w-7xl px-6 lg:px-8","children":["$","div",null,{"className":"mx-auto max-w-4xl text-center","children":[["$","h1",null,{"className":"text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl","children":["Our ",["$","span",null,{"className":"bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent","children":"Projects"}]]}],["$","p",null,{"className":"mt-6 text-lg leading-8 text-text-secondary sm:text-xl max-w-3xl mx-auto","children":"Discover how we transform ideas into intelligent solutions. From AI platforms to automation systems, each project showcases our commitment to innovation and excellence."}]]}]}]}],["$","section",null,{"className":"pb-24 sm:pb-32","children":["$","div",null,{"className":"mx-auto max-w-7xl px-6 lg:px-8","children":["$","$7",null,{"fallback":["$","div",null,{"className":"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3","children":[["$","div","0",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}],["$","div","1",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}],["$","div","2",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}],["$","div","3",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}],["$","div","4",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}],["$","div","5",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm bg-surface-alt border-secondary/20 animate-pulse","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-3/4"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-full"}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-2/3"}],["$","div",null,{"className":"h-6 bg-text-secondary/20 rounded w-20"}]]}]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":["$","div",null,{"className":"space-y-3","children":[["$","div",null,{"className":"flex gap-2","children":[["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-16"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-20"}],["$","div",null,{"className":"h-5 bg-text-secondary/20 rounded-full w-14"}]]}],["$","div",null,{"className":"h-4 bg-text-secondary/20 rounded w-32"}],["$","div",null,{"className":"h-10 bg-text-secondary/20 rounded w-full"}]]}]}]]}]]}],"children":"$L8"}]}]}]]}],null,["$","$L9",null,{"children":["$La","$Lb",["$","$Lc",null,{"promise":"$@d"}]]}]]}],{},null,false]},null,false]},null,false],["$","$1","h",{"children":[null,["$","$1","C3LV5Nn7xBZTGuOSZ1AeMv",{"children":[["$","$Le",null,{"children":"$Lf"}],null]}],["$","$L10",null,{"children":"$L11"}]]}],false]],"m":"$undefined","G":["$12","$undefined"],"s":false,"S":true}
13:I[74911,[],"AsyncMetadata"]
11:["$","div",null,{"hidden":true,"children":["$","$7",null,{"fallback":null,"children":["$","$L13",null,{"promise":"$@14"}]}]}]
b:null
f:[["$","meta","0",{"charSet":"utf-8"}],["$","meta","1",{"name":"viewport","content":"width=device-width, initial-scale=1"}]]
a:null
d:{"metadata":[["$","title","0",{"children":"Projects | Metamorphic Labs"}],["$","meta","1",{"name":"description","content":"Explore our portfolio of AI-powered solutions, intelligent automation systems, and custom software projects."}],["$","meta","2",{"name":"author","content":"Metamorphic Labs LLC"}],["$","meta","3",{"name":"keywords","content":"AI, machine learning, software development, automation, custom software, AI platforms"}],["$","meta","4",{"name":"creator","content":"Metamorphic Labs LLC"}],["$","meta","5",{"name":"publisher","content":"Metamorphic Labs LLC"}],["$","meta","6",{"name":"robots","content":"index, follow"}],["$","meta","7",{"name":"googlebot","content":"index, follow, max-video-preview:-1, max-image-preview:large, max-snippet:-1"}],["$","meta","8",{"property":"og:title","content":"Metamorphic Labs | AI-Powered Software Solutions"}],["$","meta","9",{"property":"og:description","content":"Transforming ideas into intelligent solutions through adaptive AI technology."}],["$","meta","10",{"property":"og:url","content":"https://metamorphiclabs.com"}],["$","meta","11",{"property":"og:site_name","content":"Metamorphic Labs"}],["$","meta","12",{"property":"og:locale","content":"en_US"}],["$","meta","13",{"property":"og:type","content":"website"}],["$","meta","14",{"name":"twitter:card","content":"summary_large_image"}],["$","meta","15",{"name":"twitter:creator","content":"@metamorphiclabs"}],["$","meta","16",{"name":"twitter:title","content":"Metamorphic Labs | AI-Powered Software Solutions"}],["$","meta","17",{"name":"twitter:description","content":"Transforming ideas into intelligent solutions through adaptive AI technology."}],["$","link","18",{"rel":"icon","href":"/favicon.ico","type":"image/x-icon","sizes":"16x16"}]],"error":null,"digest":"$undefined"}
14:{"metadata":"$d:metadata","error":null,"digest":"$undefined"}
8:["$","div",null,{"className":"grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3","children":[["$","div","ai-integration-broker",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10 md:col-span-2 lg:col-span-1","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"AI Integration & Broker System"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"A smart dispatcher that routes user queries to the optimal LLM (ChatGPT, Claude, Perplexity, Grok) and stitches multi-agent collaboration chains. OAuth & secure token vault are complete; SaaS workflow plumbing and PromptBox export are next."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-accent/15 text-accent border-accent/20","children":"Phase 2 – Broker Complete"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","Multi-LLM",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Multi-LLM"]}],["$","span","OAuth",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"OAuth"]}],["$","span","Routing",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Routing"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/ai-integration-broker","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}],["$","div","metamorphic-saas-suite",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"Metamorphic SaaS Suite"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"An AI-first factory that auto-generates production-grade SaaS apps from user prompts. Features curated build pipelines, blockchain revenue sharing via EFX tokens, and compliance-aware blueprints."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-tertiary/15 text-tertiary border-tertiary/20","children":"Concept"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","On-Demand SaaS",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"On-Demand SaaS"]}],["$","span","AI Assembly",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"AI Assembly"]}],["$","span","EFX Revenue Share",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"EFX Revenue Share"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/metamorphic-saas-suite","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}],["$","div","living-pipeline",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"Living Pipeline (AI-Optimized CI/CD)"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"A \"living\" event-driven CI/CD stack that uses ML to spot flakiness, optimize build caches, and choose rollout strategies (blue-green, canary) on the fly."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-tertiary/15 text-tertiary border-tertiary/20","children":"Concept"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","CI/CD",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"CI/CD"]}],["$","span","Anomaly Detection",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Anomaly Detection"]}],["$","span","Self-Optimizing",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Self-Optimizing"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/living-pipeline","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}],["$","div","metamorphic-reactor",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10 md:col-span-2 lg:col-span-1","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"Metamorphic Reactor VS Code Extension"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"A multi-agent, multi-LLM VS Code extension that automates coding tasks, powered by a \"Meta-Block\" consensus engine and deep audit trails."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-accent/15 text-accent border-accent/20","children":"Beta v0.9.0"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","Multi-Agent",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Multi-Agent"]}],["$","span","IDE Plugin",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"IDE Plugin"]}],["$","span","Consensus",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Consensus"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/metamorphic-reactor","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}],["$","div","metamorphic-testing",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"Metamorphic Testing Framework"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"A metamorphic testing suite ensuring AI stability under perturbed inputs, with real-time MR-based debugging and self-adaptive validation loops."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-tertiary/15 text-tertiary border-tertiary/20","children":"Concept"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","Metamorphic Testing",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Metamorphic Testing"]}],["$","span","ML Validation",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"ML Validation"]}],["$","span","Self-Healing",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Self-Healing"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/metamorphic-testing","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}],["$","div","metamorphic-ai-platform",{"data-slot":"card","className":"text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm group bg-surface-alt border-secondary/20 hover:border-secondary/40 transition-all duration-300 hover:shadow-lg hover:shadow-secondary/10","children":[["$","div",null,{"data-slot":"card-header","className":"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6 pb-4","children":[["$","div",null,{"className":"flex items-start justify-between","children":["$","div",null,{"className":"flex-1","children":[["$","div",null,{"data-slot":"card-title","className":"text-xl font-bold text-text-primary group-hover:text-secondary transition-colors","children":"Metamorphic AI Platform"}],["$","div",null,{"data-slot":"card-description","className":"text-sm mt-2 text-text-secondary line-clamp-2","children":"Metamorphic Labs' flagship R&D framework—an adaptive AI tool-suite featuring multi-model chaining, automated prompt optimization, custom model training, and privacy-preserving federated networks."}]]}]}],["$","div",null,{"className":"flex items-center gap-2 mt-4","children":["$","span",null,{"data-slot":"badge","className":"inline-flex items-center justify-center rounded-md border font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden [a&]:hover:bg-secondary/90 text-xs px-2 py-1 bg-tertiary/15 text-tertiary border-tertiary/20","children":"Concept / Design"}]}]]}],["$","div",null,{"data-slot":"card-content","className":"px-6 pt-0","children":[["$","div",null,{"className":"flex flex-wrap gap-2 mb-4","children":[[["$","span","AI Toolchain",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"AI Toolchain"]}],["$","span","Model Fusion",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Model Fusion"]}],["$","span","Prompt Optimization",{"className":"inline-flex items-center rounded-full bg-tertiary/10 px-2 py-1 text-xs font-medium text-tertiary","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-tag mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","vktsd0",{"d":"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z"}],["$","circle","kqv944",{"cx":"7.5","cy":"7.5","r":".5","fill":"currentColor"}],"$undefined"]}],"Prompt Optimization"]}]],false]}],["$","div",null,{"className":"flex items-center text-xs text-text-secondary mb-4","children":[["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-calendar mr-1 h-3 w-3","aria-hidden":"true","children":[["$","path","1cmpym",{"d":"M8 2v4"}],["$","path","4m81vk",{"d":"M16 2v4"}],["$","rect","1hopcy",{"width":"18","height":"18","x":"3","y":"4","rx":"2"}],["$","path","8toen8",{"d":"M3 10h18"}],"$undefined"]}],"June 21, 2025"]}],["$","$L6",null,{"href":"/projects/metamorphic-ai-platform","children":[["$","span",null,{"children":"Learn More"}],["$","svg",null,{"ref":"$undefined","xmlns":"http://www.w3.org/2000/svg","width":24,"height":24,"viewBox":"0 0 24 24","fill":"none","stroke":"currentColor","strokeWidth":2,"strokeLinecap":"round","strokeLinejoin":"round","className":"lucide lucide-arrow-right h-4 w-4 transition-transform group-hover/btn:translate-x-1","aria-hidden":"true","children":[["$","path","1ays0h",{"d":"M5 12h14"}],["$","path","xquz4c",{"d":"m12 5 7 7-7 7"}],"$undefined"]}]],"data-slot":"button","className":"inline-flex items-center gap-2 whitespace-nowrap rounded-md text-sm font-medium disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:hover:bg-accent/50 h-9 px-4 py-2 has-[>svg]:px-3 w-full justify-between text-text-primary hover:text-secondary hover:bg-secondary/5 transition-all duration-300 group/btn","ref":null}]]}]]}]]}]
