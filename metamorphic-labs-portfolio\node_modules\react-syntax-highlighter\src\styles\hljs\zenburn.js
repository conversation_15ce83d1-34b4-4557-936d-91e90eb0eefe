export default {
    "hljs": {
        "display": "block",
        "overflowX": "auto",
        "padding": "0.5em",
        "background": "#3f3f3f",
        "color": "#dcdcdc"
    },
    "hljs-keyword": {
        "color": "#e3ceab"
    },
    "hljs-selector-tag": {
        "color": "#e3ceab"
    },
    "hljs-tag": {
        "color": "#e3ceab"
    },
    "hljs-template-tag": {
        "color": "#dcdcdc"
    },
    "hljs-number": {
        "color": "#8cd0d3"
    },
    "hljs-variable": {
        "color": "#efdcbc"
    },
    "hljs-template-variable": {
        "color": "#efdcbc"
    },
    "hljs-attribute": {
        "color": "#efdcbc"
    },
    "hljs-literal": {
        "color": "#efefaf"
    },
    "hljs-subst": {
        "color": "#8f8f8f"
    },
    "hljs-title": {
        "color": "#efef8f"
    },
    "hljs-name": {
        "color": "#efef8f"
    },
    "hljs-selector-id": {
        "color": "#efef8f"
    },
    "hljs-selector-class": {
        "color": "#efef8f"
    },
    "hljs-section": {
        "color": "#efef8f"
    },
    "hljs-type": {
        "color": "#efef8f"
    },
    "hljs-symbol": {
        "color": "#dca3a3"
    },
    "hljs-bullet": {
        "color": "#dca3a3"
    },
    "hljs-link": {
        "color": "#dca3a3"
    },
    "hljs-deletion": {
        "color": "#cc9393"
    },
    "hljs-string": {
        "color": "#cc9393"
    },
    "hljs-built_in": {
        "color": "#cc9393"
    },
    "hljs-builtin-name": {
        "color": "#cc9393"
    },
    "hljs-addition": {
        "color": "#7f9f7f"
    },
    "hljs-comment": {
        "color": "#7f9f7f"
    },
    "hljs-quote": {
        "color": "#7f9f7f"
    },
    "hljs-meta": {
        "color": "#7f9f7f"
    },
    "hljs-emphasis": {
        "fontStyle": "italic"
    },
    "hljs-strong": {
        "fontWeight": "bold"
    }
}