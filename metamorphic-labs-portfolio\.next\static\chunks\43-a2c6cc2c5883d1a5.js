"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[43],{9428:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},16785:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("target",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["circle",{cx:"12",cy:"12",r:"6",key:"1vlfrh"}],["circle",{cx:"12",cy:"12",r:"2",key:"1c9p78"}]])},18175:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]])},40646:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},49376:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]])},51934:(t,e,i)=>{let s;function n(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function r(t){let e=[{},{}];return t?.values.forEach((t,i)=>{e[0][i]=t.get(),e[1][i]=t.getVelocity()}),e}function a(t,e,i,s){if("function"==typeof e){let[n,a]=r(s);e=e(void 0!==i?i:t.custom,n,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){let[n,a]=r(s);e=e(void 0!==i?i:t.custom,n,a)}return e}function o(t,e,i){let s=t.getProps();return a(s,e,void 0!==i?i:s.custom,t)}function l(t,e){return t?.[e]??t?.default??t}i.d(e,{P:()=>rS});let h=t=>t,u={},d=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],c={value:null,addProjectionMetrics:null};function p(t,e){let i=!1,s=!0,n={delta:0,timestamp:0,isProcessing:!1},r=()=>i=!0,a=d.reduce((t,i)=>(t[i]=function(t,e){let i=new Set,s=new Set,n=!1,r=!1,a=new WeakSet,o={delta:0,timestamp:0,isProcessing:!1},l=0;function h(e){a.has(e)&&(u.schedule(e),t()),l++,e(o)}let u={schedule:(t,e=!1,r=!1)=>{let o=r&&n?i:s;return e&&a.add(t),o.has(t)||o.add(t),t},cancel:t=>{s.delete(t),a.delete(t)},process:t=>{if(o=t,n){r=!0;return}n=!0,[i,s]=[s,i],i.forEach(h),e&&c.value&&c.value.frameloop[e].push(l),l=0,i.clear(),n=!1,r&&(r=!1,u.process(t))}};return u}(r,e?i:void 0),t),{}),{setup:o,read:l,resolveKeyframes:h,preUpdate:p,update:m,preRender:f,render:y,postRender:g}=a,v=()=>{let r=u.useManualTiming?n.timestamp:performance.now();i=!1,u.useManualTiming||(n.delta=s?1e3/60:Math.max(Math.min(r-n.timestamp,40),1)),n.timestamp=r,n.isProcessing=!0,o.process(n),l.process(n),h.process(n),p.process(n),m.process(n),f.process(n),y.process(n),g.process(n),n.isProcessing=!1,i&&e&&(s=!1,t(v))},x=()=>{i=!0,s=!0,n.isProcessing||t(v)};return{schedule:d.reduce((t,e)=>{let s=a[e];return t[e]=(t,e=!1,n=!1)=>(i||x(),s.schedule(t,e,n)),t},{}),cancel:t=>{for(let e=0;e<d.length;e++)a[d[e]].cancel(t)},state:n,steps:a}}let{schedule:m,cancel:f,state:y,steps:g}=p("undefined"!=typeof requestAnimationFrame?requestAnimationFrame:h,!0),v=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],x=new Set(v),T=new Set(["width","height","top","left","right","bottom",...v]);function w(t,e){-1===t.indexOf(e)&&t.push(e)}function b(t,e){let i=t.indexOf(e);i>-1&&t.splice(i,1)}class P{constructor(){this.subscriptions=[]}add(t){return w(this.subscriptions,t),()=>b(this.subscriptions,t)}notify(t,e,i){let s=this.subscriptions.length;if(s)if(1===s)this.subscriptions[0](t,e,i);else for(let n=0;n<s;n++){let s=this.subscriptions[n];s&&s(t,e,i)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}function A(){s=void 0}let S={now:()=>(void 0===s&&S.set(y.isProcessing||u.useManualTiming?y.timestamp:performance.now()),s),set:t=>{s=t,queueMicrotask(A)}},M=t=>!isNaN(parseFloat(t)),V={current:void 0};class k{constructor(t,e={}){this.canTrackVelocity=null,this.events={},this.updateAndNotify=(t,e=!0)=>{let i=S.now();if(this.updatedAt!==i&&this.setPrevFrameValue(),this.prev=this.current,this.setCurrent(t),this.current!==this.prev&&(this.events.change?.notify(this.current),this.dependents))for(let t of this.dependents)t.dirty();e&&this.events.renderRequest?.notify(this.current)},this.hasAnimated=!1,this.setCurrent(t),this.owner=e.owner}setCurrent(t){this.current=t,this.updatedAt=S.now(),null===this.canTrackVelocity&&void 0!==t&&(this.canTrackVelocity=M(this.current))}setPrevFrameValue(t=this.current){this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt}onChange(t){return this.on("change",t)}on(t,e){this.events[t]||(this.events[t]=new P);let i=this.events[t].add(e);return"change"===t?()=>{i(),m.read(()=>{this.events.change.getSize()||this.stop()})}:i}clearListeners(){for(let t in this.events)this.events[t].clear()}attach(t,e){this.passiveEffect=t,this.stopPassiveEffect=e}set(t,e=!0){e&&this.passiveEffect?this.passiveEffect(t,this.updateAndNotify):this.updateAndNotify(t,e)}setWithVelocity(t,e,i){this.set(e),this.prev=void 0,this.prevFrameValue=t,this.prevUpdatedAt=this.updatedAt-i}jump(t,e=!0){this.updateAndNotify(t),this.prev=t,this.prevUpdatedAt=this.prevFrameValue=void 0,e&&this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}dirty(){this.events.change?.notify(this.current)}addDependent(t){this.dependents||(this.dependents=new Set),this.dependents.add(t)}removeDependent(t){this.dependents&&this.dependents.delete(t)}get(){return V.current&&V.current.push(this),this.current}getPrevious(){return this.prev}getVelocity(){var t;let e=S.now();if(!this.canTrackVelocity||void 0===this.prevFrameValue||e-this.updatedAt>30)return 0;let i=Math.min(this.updatedAt-this.prevUpdatedAt,30);return t=parseFloat(this.current)-parseFloat(this.prevFrameValue),i?1e3/i*t:0}start(t){return this.stop(),new Promise(e=>{this.hasAnimated=!0,this.animation=t(e),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.dependents?.clear(),this.events.destroy?.notify(),this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function E(t,e){return new k(t,e)}let D=t=>Array.isArray(t),C=t=>!!(t&&t.getVelocity);function R(t,e){let i=t.getValue("willChange");if(C(i)&&i.add)return i.add(e);if(!i&&u.WillChange){let i=new u.WillChange("auto");t.addValue("willChange",i),i.add(e)}}let j=t=>t.replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase(),L="data-"+j("framerAppearId"),F=(t,e)=>i=>e(t(i)),B=(...t)=>t.reduce(F),O=(t,e,i)=>i>e?e:i<t?t:i,I=t=>1e3*t,U=t=>t/1e3,N={layout:0,mainThread:0,waapi:0},$=()=>{},W=()=>{},z=t=>e=>"string"==typeof e&&e.startsWith(t),Y=z("--"),H=z("var(--"),X=t=>!!H(t)&&q.test(t.split("/*")[0].trim()),q=/var\(--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)$/iu,K={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},_={...K,transform:t=>O(0,1,t)},G={...K,default:1},Z=t=>Math.round(1e5*t)/1e5,J=/-?(?:\d+(?:\.\d+)?|\.\d+)/gu,Q=/^(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))$/iu,tt=(t,e)=>i=>!!("string"==typeof i&&Q.test(i)&&i.startsWith(t)||e&&null!=i&&Object.prototype.hasOwnProperty.call(i,e)),te=(t,e,i)=>s=>{if("string"!=typeof s)return s;let[n,r,a,o]=s.match(J);return{[t]:parseFloat(n),[e]:parseFloat(r),[i]:parseFloat(a),alpha:void 0!==o?parseFloat(o):1}},ti=t=>O(0,255,t),ts={...K,transform:t=>Math.round(ti(t))},tn={test:tt("rgb","red"),parse:te("red","green","blue"),transform:({red:t,green:e,blue:i,alpha:s=1})=>"rgba("+ts.transform(t)+", "+ts.transform(e)+", "+ts.transform(i)+", "+Z(_.transform(s))+")"},tr={test:tt("#"),parse:function(t){let e="",i="",s="",n="";return t.length>5?(e=t.substring(1,3),i=t.substring(3,5),s=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),i=t.substring(2,3),s=t.substring(3,4),n=t.substring(4,5),e+=e,i+=i,s+=s,n+=n),{red:parseInt(e,16),green:parseInt(i,16),blue:parseInt(s,16),alpha:n?parseInt(n,16)/255:1}},transform:tn.transform},ta=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),to=ta("deg"),tl=ta("%"),th=ta("px"),tu=ta("vh"),td=ta("vw"),tc={...tl,parse:t=>tl.parse(t)/100,transform:t=>tl.transform(100*t)},tp={test:tt("hsl","hue"),parse:te("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:i,alpha:s=1})=>"hsla("+Math.round(t)+", "+tl.transform(Z(e))+", "+tl.transform(Z(i))+", "+Z(_.transform(s))+")"},tm={test:t=>tn.test(t)||tr.test(t)||tp.test(t),parse:t=>tn.test(t)?tn.parse(t):tp.test(t)?tp.parse(t):tr.parse(t),transform:t=>"string"==typeof t?t:t.hasOwnProperty("red")?tn.transform(t):tp.transform(t),getAnimatableNone:t=>{let e=tm.parse(t);return e.alpha=0,tm.transform(e)}},tf=/(?:#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\))/giu,ty="number",tg="color",tv=/var\s*\(\s*--(?:[\w-]+\s*|[\w-]+\s*,(?:\s*[^)(\s]|\s*\((?:[^)(]|\([^)(]*\))*\))+\s*)\)|#[\da-f]{3,8}|(?:rgb|hsl)a?\((?:-?[\d.]+%?[,\s]+){2}-?[\d.]+%?\s*(?:[,/]\s*)?(?:\b\d+(?:\.\d+)?|\.\d+)?%?\)|-?(?:\d+(?:\.\d+)?|\.\d+)/giu;function tx(t){let e=t.toString(),i=[],s={color:[],number:[],var:[]},n=[],r=0,a=e.replace(tv,t=>(tm.test(t)?(s.color.push(r),n.push(tg),i.push(tm.parse(t))):t.startsWith("var(")?(s.var.push(r),n.push("var"),i.push(t)):(s.number.push(r),n.push(ty),i.push(parseFloat(t))),++r,"${}")).split("${}");return{values:i,split:a,indexes:s,types:n}}function tT(t){return tx(t).values}function tw(t){let{split:e,types:i}=tx(t),s=e.length;return t=>{let n="";for(let r=0;r<s;r++)if(n+=e[r],void 0!==t[r]){let e=i[r];e===ty?n+=Z(t[r]):e===tg?n+=tm.transform(t[r]):n+=t[r]}return n}}let tb=t=>"number"==typeof t?0:tm.test(t)?tm.getAnimatableNone(t):t,tP={test:function(t){return isNaN(t)&&"string"==typeof t&&(t.match(J)?.length||0)+(t.match(tf)?.length||0)>0},parse:tT,createTransformer:tw,getAnimatableNone:function(t){let e=tT(t);return tw(t)(e.map(tb))}};function tA(t,e,i){return(i<0&&(i+=1),i>1&&(i-=1),i<1/6)?t+(e-t)*6*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function tS(t,e){return i=>i>0?e:t}let tM=(t,e,i)=>t+(e-t)*i,tV=(t,e,i)=>{let s=t*t,n=i*(e*e-s)+s;return n<0?0:Math.sqrt(n)},tk=[tr,tn,tp],tE=t=>tk.find(e=>e.test(t));function tD(t){let e=tE(t);if($(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`),!e)return!1;let i=e.parse(t);return e===tp&&(i=function({hue:t,saturation:e,lightness:i,alpha:s}){t/=360,i/=100;let n=0,r=0,a=0;if(e/=100){let s=i<.5?i*(1+e):i+e-i*e,o=2*i-s;n=tA(o,s,t+1/3),r=tA(o,s,t),a=tA(o,s,t-1/3)}else n=r=a=i;return{red:Math.round(255*n),green:Math.round(255*r),blue:Math.round(255*a),alpha:s}}(i)),i}let tC=(t,e)=>{let i=tD(t),s=tD(e);if(!i||!s)return tS(t,e);let n={...i};return t=>(n.red=tV(i.red,s.red,t),n.green=tV(i.green,s.green,t),n.blue=tV(i.blue,s.blue,t),n.alpha=tM(i.alpha,s.alpha,t),tn.transform(n))},tR=new Set(["none","hidden"]);function tj(t,e){return i=>tM(t,e,i)}function tL(t){return"number"==typeof t?tj:"string"==typeof t?X(t)?tS:tm.test(t)?tC:tO:Array.isArray(t)?tF:"object"==typeof t?tm.test(t)?tC:tB:tS}function tF(t,e){let i=[...t],s=i.length,n=t.map((t,i)=>tL(t)(t,e[i]));return t=>{for(let e=0;e<s;e++)i[e]=n[e](t);return i}}function tB(t,e){let i={...t,...e},s={};for(let n in i)void 0!==t[n]&&void 0!==e[n]&&(s[n]=tL(t[n])(t[n],e[n]));return t=>{for(let e in s)i[e]=s[e](t);return i}}let tO=(t,e)=>{let i=tP.createTransformer(e),s=tx(t),n=tx(e);return s.indexes.var.length===n.indexes.var.length&&s.indexes.color.length===n.indexes.color.length&&s.indexes.number.length>=n.indexes.number.length?tR.has(t)&&!n.values.length||tR.has(e)&&!s.values.length?function(t,e){return tR.has(t)?i=>i<=0?t:e:i=>i>=1?e:t}(t,e):B(tF(function(t,e){let i=[],s={color:0,var:0,number:0};for(let n=0;n<e.values.length;n++){let r=e.types[n],a=t.indexes[r][s[r]],o=t.values[a]??0;i[n]=o,s[r]++}return i}(s,n),n.values),i):($(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),tS(t,e))};function tI(t,e,i){return"number"==typeof t&&"number"==typeof e&&"number"==typeof i?tM(t,e,i):tL(t)(t,e)}let tU=t=>{let e=({timestamp:e})=>t(e);return{start:(t=!0)=>m.update(e,t),stop:()=>f(e),now:()=>y.isProcessing?y.timestamp:S.now()}},tN=(t,e,i=10)=>{let s="",n=Math.max(Math.round(e/i),2);for(let e=0;e<n;e++)s+=Math.round(1e4*t(e/(n-1)))/1e4+", ";return`linear(${s.substring(0,s.length-2)})`};function t$(t){let e=0,i=t.next(e);for(;!i.done&&e<2e4;)e+=50,i=t.next(e);return e>=2e4?1/0:e}function tW(t,e,i){var s,n;let r=Math.max(e-5,0);return s=i-t(r),(n=e-r)?1e3/n*s:0}let tz={stiffness:100,damping:10,mass:1,velocity:0,duration:800,bounce:.3,visualDuration:.3,restSpeed:{granular:.01,default:2},restDelta:{granular:.005,default:.5},minDuration:.01,maxDuration:10,minDamping:.05,maxDamping:1};function tY(t,e){return t*Math.sqrt(1-e*e)}let tH=["duration","bounce"],tX=["stiffness","damping","mass"];function tq(t,e){return e.some(e=>void 0!==t[e])}function tK(t=tz.visualDuration,e=tz.bounce){let i,s="object"!=typeof t?{visualDuration:t,keyframes:[0,1],bounce:e}:t,{restSpeed:n,restDelta:r}=s,a=s.keyframes[0],o=s.keyframes[s.keyframes.length-1],l={done:!1,value:a},{stiffness:h,damping:u,mass:d,duration:c,velocity:p,isResolvedFromDuration:m}=function(t){let e={velocity:tz.velocity,stiffness:tz.stiffness,damping:tz.damping,mass:tz.mass,isResolvedFromDuration:!1,...t};if(!tq(t,tX)&&tq(t,tH))if(t.visualDuration){let i=2*Math.PI/(1.2*t.visualDuration),s=i*i,n=2*O(.05,1,1-(t.bounce||0))*Math.sqrt(s);e={...e,mass:tz.mass,stiffness:s,damping:n}}else{let i=function({duration:t=tz.duration,bounce:e=tz.bounce,velocity:i=tz.velocity,mass:s=tz.mass}){let n,r;$(t<=I(tz.maxDuration),"Spring duration must be 10 seconds or less");let a=1-e;a=O(tz.minDamping,tz.maxDamping,a),t=O(tz.minDuration,tz.maxDuration,U(t)),a<1?(n=e=>{let s=e*a,n=s*t;return .001-(s-i)/tY(e,a)*Math.exp(-n)},r=e=>{let s=e*a*t,r=Math.pow(a,2)*Math.pow(e,2)*t,o=Math.exp(-s),l=tY(Math.pow(e,2),a);return(s*i+i-r)*o*(-n(e)+.001>0?-1:1)/l}):(n=e=>-.001+Math.exp(-e*t)*((e-i)*t+1),r=e=>t*t*(i-e)*Math.exp(-e*t));let o=function(t,e,i){let s=i;for(let i=1;i<12;i++)s-=t(s)/e(s);return s}(n,r,5/t);if(t=I(t),isNaN(o))return{stiffness:tz.stiffness,damping:tz.damping,duration:t};{let e=Math.pow(o,2)*s;return{stiffness:e,damping:2*a*Math.sqrt(s*e),duration:t}}}(t);(e={...e,...i,mass:tz.mass}).isResolvedFromDuration=!0}return e}({...s,velocity:-U(s.velocity||0)}),f=p||0,y=u/(2*Math.sqrt(h*d)),g=o-a,v=U(Math.sqrt(h/d)),x=5>Math.abs(g);if(n||(n=x?tz.restSpeed.granular:tz.restSpeed.default),r||(r=x?tz.restDelta.granular:tz.restDelta.default),y<1){let t=tY(v,y);i=e=>o-Math.exp(-y*v*e)*((f+y*v*g)/t*Math.sin(t*e)+g*Math.cos(t*e))}else if(1===y)i=t=>o-Math.exp(-v*t)*(g+(f+v*g)*t);else{let t=v*Math.sqrt(y*y-1);i=e=>{let i=Math.exp(-y*v*e),s=Math.min(t*e,300);return o-i*((f+y*v*g)*Math.sinh(s)+t*g*Math.cosh(s))/t}}let T={calculatedDuration:m&&c||null,next:t=>{let e=i(t);if(m)l.done=t>=c;else{let s=0===t?f:0;y<1&&(s=0===t?I(f):tW(i,t,e));let a=Math.abs(o-e)<=r;l.done=Math.abs(s)<=n&&a}return l.value=l.done?o:e,l},toString:()=>{let t=Math.min(t$(T),2e4),e=tN(e=>T.next(t*e).value,t,30);return t+"ms "+e},toTransition:()=>{}};return T}function t_({keyframes:t,velocity:e=0,power:i=.8,timeConstant:s=325,bounceDamping:n=10,bounceStiffness:r=500,modifyTarget:a,min:o,max:l,restDelta:h=.5,restSpeed:u}){let d,c,p=t[0],m={done:!1,value:p},f=t=>void 0!==o&&t<o||void 0!==l&&t>l,y=t=>void 0===o?l:void 0===l||Math.abs(o-t)<Math.abs(l-t)?o:l,g=i*e,v=p+g,x=void 0===a?v:a(v);x!==v&&(g=x-p);let T=t=>-g*Math.exp(-t/s),w=t=>x+T(t),b=t=>{let e=T(t),i=w(t);m.done=Math.abs(e)<=h,m.value=m.done?x:i},P=t=>{f(m.value)&&(d=t,c=tK({keyframes:[m.value,y(m.value)],velocity:tW(w,t,m.value),damping:n,stiffness:r,restDelta:h,restSpeed:u}))};return P(0),{calculatedDuration:null,next:t=>{let e=!1;return(c||void 0!==d||(e=!0,b(t),P(t)),void 0!==d&&t>=d)?c.next(t-d):(e||b(t),m)}}}tK.applyToOptions=t=>{let e=function(t,e=100,i){let s=i({...t,keyframes:[0,e]}),n=Math.min(t$(s),2e4);return{type:"keyframes",ease:t=>s.next(n*t).value/e,duration:U(n)}}(t,100,tK);return t.ease=e.ease,t.duration=I(e.duration),t.type="keyframes",t};let tG=(t,e,i)=>(((1-3*i+3*e)*t+(3*i-6*e))*t+3*e)*t;function tZ(t,e,i,s){if(t===e&&i===s)return h;let n=e=>(function(t,e,i,s,n){let r,a,o=0;do(r=tG(a=e+(i-e)/2,s,n)-t)>0?i=a:e=a;while(Math.abs(r)>1e-7&&++o<12);return a})(e,0,1,t,i);return t=>0===t||1===t?t:tG(n(t),e,s)}let tJ=tZ(.42,0,1,1),tQ=tZ(0,0,.58,1),t0=tZ(.42,0,.58,1),t1=t=>Array.isArray(t)&&"number"!=typeof t[0],t5=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2,t2=t=>e=>1-t(1-e),t3=tZ(.33,1.53,.69,.99),t4=t2(t3),t9=t5(t4),t6=t=>(t*=2)<1?.5*t4(t):.5*(2-Math.pow(2,-10*(t-1))),t8=t=>1-Math.sin(Math.acos(t)),t7=t2(t8),et=t5(t8),ee=t=>Array.isArray(t)&&"number"==typeof t[0],ei={linear:h,easeIn:tJ,easeInOut:t0,easeOut:tQ,circIn:t8,circInOut:et,circOut:t7,backIn:t4,backInOut:t9,backOut:t3,anticipate:t6},es=t=>"string"==typeof t,en=t=>{if(ee(t)){W(4===t.length,"Cubic bezier arrays must contain four numerical values.");let[e,i,s,n]=t;return tZ(e,i,s,n)}return es(t)?(W(void 0!==ei[t],`Invalid easing type '${t}'`),ei[t]):t},er=(t,e,i)=>{let s=e-t;return 0===s?1:(i-t)/s};function ea({duration:t=300,keyframes:e,times:i,ease:s="easeInOut"}){var n;let r=t1(s)?s.map(en):en(s),a={done:!1,value:e[0]},o=function(t,e,{clamp:i=!0,ease:s,mixer:n}={}){let r=t.length;if(W(r===e.length,"Both input and output ranges must be the same length"),1===r)return()=>e[0];if(2===r&&e[0]===e[1])return()=>e[1];let a=t[0]===t[1];t[0]>t[r-1]&&(t=[...t].reverse(),e=[...e].reverse());let o=function(t,e,i){let s=[],n=i||u.mix||tI,r=t.length-1;for(let i=0;i<r;i++){let r=n(t[i],t[i+1]);e&&(r=B(Array.isArray(e)?e[i]||h:e,r)),s.push(r)}return s}(e,s,n),l=o.length,d=i=>{if(a&&i<t[0])return e[0];let s=0;if(l>1)for(;s<t.length-2&&!(i<t[s+1]);s++);let n=er(t[s],t[s+1],i);return o[s](n)};return i?e=>d(O(t[0],t[r-1],e)):d}((n=i&&i.length===e.length?i:function(t){let e=[0];return!function(t,e){let i=t[t.length-1];for(let s=1;s<=e;s++){let n=er(0,e,s);t.push(tM(i,1,n))}}(e,t.length-1),e}(e),n.map(e=>e*t)),e,{ease:Array.isArray(r)?r:e.map(()=>r||t0).splice(0,e.length-1)});return{calculatedDuration:t,next:e=>(a.value=o(e),a.done=e>=t,a)}}let eo=t=>null!==t;function el(t,{repeat:e,repeatType:i="loop"},s,n=1){let r=t.filter(eo),a=n<0||e&&"loop"!==i&&e%2==1?0:r.length-1;return a&&void 0!==s?s:r[a]}let eh={decay:t_,inertia:t_,tween:ea,keyframes:ea,spring:tK};function eu(t){"string"==typeof t.type&&(t.type=eh[t.type])}class ed{constructor(){this.updateFinished()}get finished(){return this._finished}updateFinished(){this._finished=new Promise(t=>{this.resolve=t})}notifyFinished(){this.resolve()}then(t,e){return this.finished.then(t,e)}}let ec=t=>t/100;class ep extends ed{constructor(t){super(),this.state="idle",this.startTime=null,this.isStopped=!1,this.currentTime=0,this.holdTime=null,this.playbackSpeed=1,this.stop=()=>{let{motionValue:t}=this.options;t&&t.updatedAt!==S.now()&&this.tick(S.now()),this.isStopped=!0,"idle"!==this.state&&(this.teardown(),this.options.onStop?.())},N.mainThread++,this.options=t,this.initAnimation(),this.play(),!1===t.autoplay&&this.pause()}initAnimation(){let{options:t}=this;eu(t);let{type:e=ea,repeat:i=0,repeatDelay:s=0,repeatType:n,velocity:r=0}=t,{keyframes:a}=t,o=e||ea;o!==ea&&"number"!=typeof a[0]&&(this.mixKeyframes=B(ec,tI(a[0],a[1])),a=[0,100]);let l=o({...t,keyframes:a});"mirror"===n&&(this.mirroredGenerator=o({...t,keyframes:[...a].reverse(),velocity:-r})),null===l.calculatedDuration&&(l.calculatedDuration=t$(l));let{calculatedDuration:h}=l;this.calculatedDuration=h,this.resolvedDuration=h+s,this.totalDuration=this.resolvedDuration*(i+1)-s,this.generator=l}updateTime(t){let e=Math.round(t-this.startTime)*this.playbackSpeed;null!==this.holdTime?this.currentTime=this.holdTime:this.currentTime=e}tick(t,e=!1){let{generator:i,totalDuration:s,mixKeyframes:n,mirroredGenerator:r,resolvedDuration:a,calculatedDuration:o}=this;if(null===this.startTime)return i.next(0);let{delay:l=0,keyframes:h,repeat:u,repeatType:d,repeatDelay:c,type:p,onUpdate:m,finalKeyframe:f}=this.options;this.speed>0?this.startTime=Math.min(this.startTime,t):this.speed<0&&(this.startTime=Math.min(t-s/this.speed,this.startTime)),e?this.currentTime=t:this.updateTime(t);let y=this.currentTime-l*(this.playbackSpeed>=0?1:-1),g=this.playbackSpeed>=0?y<0:y>s;this.currentTime=Math.max(y,0),"finished"===this.state&&null===this.holdTime&&(this.currentTime=s);let v=this.currentTime,x=i;if(u){let t=Math.min(this.currentTime,s)/a,e=Math.floor(t),i=t%1;!i&&t>=1&&(i=1),1===i&&e--,(e=Math.min(e,u+1))%2&&("reverse"===d?(i=1-i,c&&(i-=c/a)):"mirror"===d&&(x=r)),v=O(0,1,i)*a}let T=g?{done:!1,value:h[0]}:x.next(v);n&&(T.value=n(T.value));let{done:w}=T;g||null===o||(w=this.playbackSpeed>=0?this.currentTime>=s:this.currentTime<=0);let b=null===this.holdTime&&("finished"===this.state||"running"===this.state&&w);return b&&p!==t_&&(T.value=el(h,this.options,f,this.speed)),m&&m(T.value),b&&this.finish(),T}then(t,e){return this.finished.then(t,e)}get duration(){return U(this.calculatedDuration)}get time(){return U(this.currentTime)}set time(t){t=I(t),this.currentTime=t,null===this.startTime||null!==this.holdTime||0===this.playbackSpeed?this.holdTime=t:this.driver&&(this.startTime=this.driver.now()-t/this.playbackSpeed),this.driver?.start(!1)}get speed(){return this.playbackSpeed}set speed(t){this.updateTime(S.now());let e=this.playbackSpeed!==t;this.playbackSpeed=t,e&&(this.time=U(this.currentTime))}play(){if(this.isStopped)return;let{driver:t=tU,startTime:e}=this.options;this.driver||(this.driver=t(t=>this.tick(t))),this.options.onPlay?.();let i=this.driver.now();"finished"===this.state?(this.updateFinished(),this.startTime=i):null!==this.holdTime?this.startTime=i-this.holdTime:this.startTime||(this.startTime=e??i),"finished"===this.state&&this.speed<0&&(this.startTime+=this.calculatedDuration),this.holdTime=null,this.state="running",this.driver.start()}pause(){this.state="paused",this.updateTime(S.now()),this.holdTime=this.currentTime}complete(){"running"!==this.state&&this.play(),this.state="finished",this.holdTime=null}finish(){this.notifyFinished(),this.teardown(),this.state="finished",this.options.onComplete?.()}cancel(){this.holdTime=null,this.startTime=0,this.tick(0),this.teardown(),this.options.onCancel?.()}teardown(){this.state="idle",this.stopDriver(),this.startTime=this.holdTime=null,N.mainThread--}stopDriver(){this.driver&&(this.driver.stop(),this.driver=void 0)}sample(t){return this.startTime=0,this.tick(t,!0)}attachTimeline(t){return this.options.allowFlatten&&(this.options.type="keyframes",this.options.ease="linear",this.initAnimation()),this.driver?.stop(),t.observe(this)}}let em=t=>180*t/Math.PI,ef=t=>eg(em(Math.atan2(t[1],t[0]))),ey={x:4,y:5,translateX:4,translateY:5,scaleX:0,scaleY:3,scale:t=>(Math.abs(t[0])+Math.abs(t[3]))/2,rotate:ef,rotateZ:ef,skewX:t=>em(Math.atan(t[1])),skewY:t=>em(Math.atan(t[2])),skew:t=>(Math.abs(t[1])+Math.abs(t[2]))/2},eg=t=>((t%=360)<0&&(t+=360),t),ev=t=>Math.sqrt(t[0]*t[0]+t[1]*t[1]),ex=t=>Math.sqrt(t[4]*t[4]+t[5]*t[5]),eT={x:12,y:13,z:14,translateX:12,translateY:13,translateZ:14,scaleX:ev,scaleY:ex,scale:t=>(ev(t)+ex(t))/2,rotateX:t=>eg(em(Math.atan2(t[6],t[5]))),rotateY:t=>eg(em(Math.atan2(-t[2],t[0]))),rotateZ:ef,rotate:ef,skewX:t=>em(Math.atan(t[4])),skewY:t=>em(Math.atan(t[1])),skew:t=>(Math.abs(t[1])+Math.abs(t[4]))/2};function ew(t){return+!!t.includes("scale")}function eb(t,e){let i,s;if(!t||"none"===t)return ew(e);let n=t.match(/^matrix3d\(([-\d.e\s,]+)\)$/u);if(n)i=eT,s=n;else{let e=t.match(/^matrix\(([-\d.e\s,]+)\)$/u);i=ey,s=e}if(!s)return ew(e);let r=i[e],a=s[1].split(",").map(eA);return"function"==typeof r?r(a):a[r]}let eP=(t,e)=>{let{transform:i="none"}=getComputedStyle(t);return eb(i,e)};function eA(t){return parseFloat(t.trim())}let eS=t=>t===K||t===th,eM=new Set(["x","y","z"]),eV=v.filter(t=>!eM.has(t)),ek={width:({x:t},{paddingLeft:e="0",paddingRight:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),height:({y:t},{paddingTop:e="0",paddingBottom:i="0"})=>t.max-t.min-parseFloat(e)-parseFloat(i),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:(t,{transform:e})=>eb(e,"x"),y:(t,{transform:e})=>eb(e,"y")};ek.translateX=ek.x,ek.translateY=ek.y;let eE=new Set,eD=!1,eC=!1,eR=!1;function ej(){if(eC){let t=Array.from(eE).filter(t=>t.needsMeasurement),e=new Set(t.map(t=>t.element)),i=new Map;e.forEach(t=>{let e=function(t){let e=[];return eV.forEach(i=>{let s=t.getValue(i);void 0!==s&&(e.push([i,s.get()]),s.set(+!!i.startsWith("scale")))}),e}(t);e.length&&(i.set(t,e),t.render())}),t.forEach(t=>t.measureInitialState()),e.forEach(t=>{t.render();let e=i.get(t);e&&e.forEach(([e,i])=>{t.getValue(e)?.set(i)})}),t.forEach(t=>t.measureEndState()),t.forEach(t=>{void 0!==t.suspendedScrollY&&window.scrollTo(0,t.suspendedScrollY)})}eC=!1,eD=!1,eE.forEach(t=>t.complete(eR)),eE.clear()}function eL(){eE.forEach(t=>{t.readKeyframes(),t.needsMeasurement&&(eC=!0)})}class eF{constructor(t,e,i,s,n,r=!1){this.state="pending",this.isAsync=!1,this.needsMeasurement=!1,this.unresolvedKeyframes=[...t],this.onComplete=e,this.name=i,this.motionValue=s,this.element=n,this.isAsync=r}scheduleResolve(){this.state="scheduled",this.isAsync?(eE.add(this),eD||(eD=!0,m.read(eL),m.resolveKeyframes(ej))):(this.readKeyframes(),this.complete())}readKeyframes(){let{unresolvedKeyframes:t,name:e,element:i,motionValue:s}=this;if(null===t[0]){let n=s?.get(),r=t[t.length-1];if(void 0!==n)t[0]=n;else if(i&&e){let s=i.readValue(e,r);null!=s&&(t[0]=s)}void 0===t[0]&&(t[0]=r),s&&void 0===n&&s.set(t[0])}for(let e=1;e<t.length;e++)t[e]??(t[e]=t[e-1])}setFinalKeyframe(){}measureInitialState(){}renderEndStyles(){}measureEndState(){}complete(t=!1){this.state="complete",this.onComplete(this.unresolvedKeyframes,this.finalKeyframe,t),eE.delete(this)}cancel(){"scheduled"===this.state&&(eE.delete(this),this.state="pending")}resume(){"pending"===this.state&&this.scheduleResolve()}}let eB=t=>t.startsWith("--");function eO(t){let e;return()=>(void 0===e&&(e=t()),e)}let eI=eO(()=>void 0!==window.ScrollTimeline),eU={},eN=function(t,e){let i=eO(t);return()=>eU[e]??i()}(()=>{try{document.createElement("div").animate({opacity:0},{easing:"linear(0, 1)"})}catch(t){return!1}return!0},"linearEasing"),e$=([t,e,i,s])=>`cubic-bezier(${t}, ${e}, ${i}, ${s})`,eW={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:e$([0,.65,.55,1]),circOut:e$([.55,0,1,.45]),backIn:e$([.31,.01,.66,-.59]),backOut:e$([.33,1.53,.69,.99])};function ez(t){return"function"==typeof t&&"applyToOptions"in t}class eY extends ed{constructor(t){if(super(),this.finishedTime=null,this.isStopped=!1,!t)return;let{element:e,name:i,keyframes:s,pseudoElement:n,allowFlatten:r=!1,finalKeyframe:a,onComplete:o}=t;this.isPseudoElement=!!n,this.allowFlatten=r,this.options=t,W("string"!=typeof t.type,'animateMini doesn\'t support "type" as a string. Did you mean to import { spring } from "motion"?');let l=function({type:t,...e}){return ez(t)&&eN()?t.applyToOptions(e):(e.duration??(e.duration=300),e.ease??(e.ease="easeOut"),e)}(t);this.animation=function(t,e,i,{delay:s=0,duration:n=300,repeat:r=0,repeatType:a="loop",ease:o="easeOut",times:l}={},h){let u={[e]:i};l&&(u.offset=l);let d=function t(e,i){if(e)return"function"==typeof e?eN()?tN(e,i):"ease-out":ee(e)?e$(e):Array.isArray(e)?e.map(e=>t(e,i)||eW.easeOut):eW[e]}(o,n);Array.isArray(d)&&(u.easing=d),c.value&&N.waapi++;let p={delay:s,duration:n,easing:Array.isArray(d)?"linear":d,fill:"both",iterations:r+1,direction:"reverse"===a?"alternate":"normal"};h&&(p.pseudoElement=h);let m=t.animate(u,p);return c.value&&m.finished.finally(()=>{N.waapi--}),m}(e,i,s,l,n),!1===l.autoplay&&this.animation.pause(),this.animation.onfinish=()=>{if(this.finishedTime=this.time,!n){let t=el(s,this.options,a,this.speed);this.updateMotionValue?this.updateMotionValue(t):function(t,e,i){eB(e)?t.style.setProperty(e,i):t.style[e]=i}(e,i,t),this.animation.cancel()}o?.(),this.notifyFinished()}}play(){this.isStopped||(this.animation.play(),"finished"===this.state&&this.updateFinished())}pause(){this.animation.pause()}complete(){this.animation.finish?.()}cancel(){try{this.animation.cancel()}catch(t){}}stop(){if(this.isStopped)return;this.isStopped=!0;let{state:t}=this;"idle"!==t&&"finished"!==t&&(this.updateMotionValue?this.updateMotionValue():this.commitStyles(),this.isPseudoElement||this.cancel())}commitStyles(){this.isPseudoElement||this.animation.commitStyles?.()}get duration(){return U(Number(this.animation.effect?.getComputedTiming?.().duration||0))}get time(){return U(Number(this.animation.currentTime)||0)}set time(t){this.finishedTime=null,this.animation.currentTime=I(t)}get speed(){return this.animation.playbackRate}set speed(t){t<0&&(this.finishedTime=null),this.animation.playbackRate=t}get state(){return null!==this.finishedTime?"finished":this.animation.playState}get startTime(){return Number(this.animation.startTime)}set startTime(t){this.animation.startTime=t}attachTimeline({timeline:t,observe:e}){return(this.allowFlatten&&this.animation.effect?.updateTiming({easing:"linear"}),this.animation.onfinish=null,t&&eI())?(this.animation.timeline=t,h):e(this)}}let eH={anticipate:t6,backInOut:t9,circInOut:et};class eX extends eY{constructor(t){!function(t){"string"==typeof t.ease&&t.ease in eH&&(t.ease=eH[t.ease])}(t),eu(t),super(t),t.startTime&&(this.startTime=t.startTime),this.options=t}updateMotionValue(t){let{motionValue:e,onUpdate:i,onComplete:s,element:n,...r}=this.options;if(!e)return;if(void 0!==t)return void e.set(t);let a=new ep({...r,autoplay:!1}),o=I(this.finishedTime??this.time);e.setWithVelocity(a.sample(o-10).value,a.sample(o).value,10),a.stop()}}let eq=(t,e)=>"zIndex"!==e&&!!("number"==typeof t||Array.isArray(t)||"string"==typeof t&&(tP.test(t)||"0"===t)&&!t.startsWith("url("));function eK(t){return"object"==typeof t&&null!==t}function e_(t){return eK(t)&&"offsetHeight"in t}let eG=new Set(["opacity","clipPath","filter","transform"]),eZ=eO(()=>Object.hasOwnProperty.call(Element.prototype,"animate"));class eJ extends ed{constructor({autoplay:t=!0,delay:e=0,type:i="keyframes",repeat:s=0,repeatDelay:n=0,repeatType:r="loop",keyframes:a,name:o,motionValue:l,element:h,...u}){super(),this.stop=()=>{this._animation&&(this._animation.stop(),this.stopTimeline?.()),this.keyframeResolver?.cancel()},this.createdAt=S.now();let d={autoplay:t,delay:e,type:i,repeat:s,repeatDelay:n,repeatType:r,name:o,motionValue:l,element:h,...u},c=h?.KeyframeResolver||eF;this.keyframeResolver=new c(a,(t,e,i)=>this.onKeyframesResolved(t,e,d,!i),o,l,h),this.keyframeResolver?.scheduleResolve()}onKeyframesResolved(t,e,i,s){this.keyframeResolver=void 0;let{name:n,type:r,velocity:a,delay:o,isHandoff:l,onUpdate:d}=i;this.resolvedAt=S.now(),!function(t,e,i,s){let n=t[0];if(null===n)return!1;if("display"===e||"visibility"===e)return!0;let r=t[t.length-1],a=eq(n,e),o=eq(r,e);return $(a===o,`You are trying to animate ${e} from "${n}" to "${r}". ${n} is not an animatable value - to enable this animation set ${n} to a value animatable to ${r} via the \`style\` property.`),!!a&&!!o&&(function(t){let e=t[0];if(1===t.length)return!0;for(let i=0;i<t.length;i++)if(t[i]!==e)return!0}(t)||("spring"===i||ez(i))&&s)}(t,n,r,a)&&((u.instantAnimations||!o)&&d?.(el(t,i,e)),t[0]=t[t.length-1],i.duration=0,i.repeat=0);let c={startTime:s?this.resolvedAt&&this.resolvedAt-this.createdAt>40?this.resolvedAt:this.createdAt:void 0,finalKeyframe:e,...i,keyframes:t},p=!l&&function(t){let{motionValue:e,name:i,repeatDelay:s,repeatType:n,damping:r,type:a}=t;if(!e_(e?.owner?.current))return!1;let{onUpdate:o,transformTemplate:l}=e.owner.getProps();return eZ()&&i&&eG.has(i)&&("transform"!==i||!l)&&!o&&!s&&"mirror"!==n&&0!==r&&"inertia"!==a}(c)?new eX({...c,element:c.motionValue.owner.current}):new ep(c);p.finished.then(()=>this.notifyFinished()).catch(h),this.pendingTimeline&&(this.stopTimeline=p.attachTimeline(this.pendingTimeline),this.pendingTimeline=void 0),this._animation=p}get finished(){return this._animation?this.animation.finished:this._finished}then(t,e){return this.finished.finally(t).then(()=>{})}get animation(){return this._animation||(this.keyframeResolver?.resume(),eR=!0,eL(),ej(),eR=!1),this._animation}get duration(){return this.animation.duration}get time(){return this.animation.time}set time(t){this.animation.time=t}get speed(){return this.animation.speed}get state(){return this.animation.state}set speed(t){this.animation.speed=t}get startTime(){return this.animation.startTime}attachTimeline(t){return this._animation?this.stopTimeline=this.animation.attachTimeline(t):this.pendingTimeline=t,()=>this.stop()}play(){this.animation.play()}pause(){this.animation.pause()}complete(){this.animation.complete()}cancel(){this._animation&&this.animation.cancel(),this.keyframeResolver?.cancel()}}let eQ=t=>null!==t,e0={type:"spring",stiffness:500,damping:25,restSpeed:10},e1=t=>({type:"spring",stiffness:550,damping:0===t?2*Math.sqrt(550):30,restSpeed:10}),e5={type:"keyframes",duration:.8},e2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},e3=(t,{keyframes:e})=>e.length>2?e5:x.has(t)?t.startsWith("scale")?e1(e[1]):e0:e2,e4=(t,e,i,s={},n,r)=>a=>{let o=l(s,t)||{},h=o.delay||s.delay||0,{elapsed:d=0}=s;d-=I(h);let c={keyframes:Array.isArray(i)?i:[null,i],ease:"easeOut",velocity:e.getVelocity(),...o,delay:-d,onUpdate:t=>{e.set(t),o.onUpdate&&o.onUpdate(t)},onComplete:()=>{a(),o.onComplete&&o.onComplete()},name:t,motionValue:e,element:r?void 0:n};!function({when:t,delay:e,delayChildren:i,staggerChildren:s,staggerDirection:n,repeat:r,repeatType:a,repeatDelay:o,from:l,elapsed:h,...u}){return!!Object.keys(u).length}(o)&&Object.assign(c,e3(t,c)),c.duration&&(c.duration=I(c.duration)),c.repeatDelay&&(c.repeatDelay=I(c.repeatDelay)),void 0!==c.from&&(c.keyframes[0]=c.from);let p=!1;if(!1!==c.type&&(0!==c.duration||c.repeatDelay)||(c.duration=0,0===c.delay&&(p=!0)),(u.instantAnimations||u.skipAnimations)&&(p=!0,c.duration=0,c.delay=0),c.allowFlatten=!o.type&&!o.ease,p&&!r&&void 0!==e.get()){let t=function(t,{repeat:e,repeatType:i="loop"},s){let n=t.filter(eQ),r=e&&"loop"!==i&&e%2==1?0:n.length-1;return n[r]}(c.keyframes,o);if(void 0!==t)return void m.update(()=>{c.onUpdate(t),c.onComplete()})}return o.isSync?new ep(c):new eJ(c)};function e9(t,e,{delay:i=0,transitionOverride:s,type:n}={}){let{transition:r=t.getDefaultTransition(),transitionEnd:a,...h}=e;s&&(r=s);let u=[],d=n&&t.animationState&&t.animationState.getState()[n];for(let e in h){let s=t.getValue(e,t.latestValues[e]??null),n=h[e];if(void 0===n||d&&function({protectedKeys:t,needsAnimating:e},i){let s=t.hasOwnProperty(i)&&!0!==e[i];return e[i]=!1,s}(d,e))continue;let a={delay:i,...l(r||{},e)},o=s.get();if(void 0!==o&&!s.isAnimating&&!Array.isArray(n)&&n===o&&!a.velocity)continue;let c=!1;if(window.MotionHandoffAnimation){let i=t.props[L];if(i){let t=window.MotionHandoffAnimation(i,e,m);null!==t&&(a.startTime=t,c=!0)}}R(t,e),s.start(e4(e,s,n,t.shouldReduceMotion&&T.has(e)?{type:!1}:a,t,c));let p=s.animation;p&&u.push(p)}return a&&Promise.all(u).then(()=>{m.update(()=>{a&&function(t,e){let{transitionEnd:i={},transition:s={},...n}=o(t,e)||{};for(let e in n={...n,...i}){var r;let i=D(r=n[e])?r[r.length-1]||0:r;t.hasValue(e)?t.getValue(e).set(i):t.addValue(e,E(i))}}(t,a)})}),u}function e6(t,e,i={}){let s=o(t,e,"exit"===i.type?t.presenceContext?.custom:void 0),{transition:n=t.getDefaultTransition()||{}}=s||{};i.transitionOverride&&(n=i.transitionOverride);let r=s?()=>Promise.all(e9(t,s,i)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(s=0)=>{let{delayChildren:r=0,staggerChildren:a,staggerDirection:o}=n;return function(t,e,i=0,s=0,n=1,r){let a=[],o=(t.variantChildren.size-1)*s,l=1===n?(t=0)=>t*s:(t=0)=>o-t*s;return Array.from(t.variantChildren).sort(e8).forEach((t,s)=>{t.notify("AnimationStart",e),a.push(e6(t,e,{...r,delay:i+l(s)}).then(()=>t.notify("AnimationComplete",e)))}),Promise.all(a)}(t,e,r+s,a,o,i)}:()=>Promise.resolve(),{when:l}=n;if(!l)return Promise.all([r(),a(i.delay)]);{let[t,e]="beforeChildren"===l?[r,a]:[a,r];return t().then(()=>e())}}function e8(t,e){return t.sortNodePosition(e)}function e7(t,e){if(!Array.isArray(e))return!1;let i=e.length;if(i!==t.length)return!1;for(let s=0;s<i;s++)if(e[s]!==t[s])return!1;return!0}function it(t){return"string"==typeof t||Array.isArray(t)}let ie=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],ii=["initial",...ie],is=ii.length,ir=[...ie].reverse(),ia=ie.length;function io(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function il(){return{animate:io(!0),whileInView:io(),whileHover:io(),whileTap:io(),whileDrag:io(),whileFocus:io(),exit:io()}}class ih{constructor(t){this.isMounted=!1,this.node=t}update(){}}class iu extends ih{constructor(t){super(t),t.animationState||(t.animationState=function(t){let e=e=>Promise.all(e.map(({animation:e,options:i})=>(function(t,e,i={}){let s;if(t.notify("AnimationStart",e),Array.isArray(e))s=Promise.all(e.map(e=>e6(t,e,i)));else if("string"==typeof e)s=e6(t,e,i);else{let n="function"==typeof e?o(t,e,i.custom):e;s=Promise.all(e9(t,n,i))}return s.then(()=>{t.notify("AnimationComplete",e)})})(t,e,i))),i=il(),s=!0,r=e=>(i,s)=>{let n=o(t,s,"exit"===e?t.presenceContext?.custom:void 0);if(n){let{transition:t,transitionEnd:e,...s}=n;i={...i,...s,...e}}return i};function a(a){let{props:l}=t,h=function t(e){if(!e)return;if(!e.isControllingVariants){let i=e.parent&&t(e.parent)||{};return void 0!==e.props.initial&&(i.initial=e.props.initial),i}let i={};for(let t=0;t<is;t++){let s=ii[t],n=e.props[s];(it(n)||!1===n)&&(i[s]=n)}return i}(t.parent)||{},u=[],d=new Set,c={},p=1/0;for(let e=0;e<ia;e++){var m,f;let o=ir[e],y=i[o],g=void 0!==l[o]?l[o]:h[o],v=it(g),x=o===a?y.isActive:null;!1===x&&(p=e);let T=g===h[o]&&g!==l[o]&&v;if(T&&s&&t.manuallyAnimateOnMount&&(T=!1),y.protectedKeys={...c},!y.isActive&&null===x||!g&&!y.prevProp||n(g)||"boolean"==typeof g)continue;let w=(m=y.prevProp,"string"==typeof(f=g)?f!==m:!!Array.isArray(f)&&!e7(f,m)),b=w||o===a&&y.isActive&&!T&&v||e>p&&v,P=!1,A=Array.isArray(g)?g:[g],S=A.reduce(r(o),{});!1===x&&(S={});let{prevResolvedValues:M={}}=y,V={...M,...S},k=e=>{b=!0,d.has(e)&&(P=!0,d.delete(e)),y.needsAnimating[e]=!0;let i=t.getValue(e);i&&(i.liveStyle=!1)};for(let t in V){let e=S[t],i=M[t];if(c.hasOwnProperty(t))continue;let s=!1;(D(e)&&D(i)?e7(e,i):e===i)?void 0!==e&&d.has(t)?k(t):y.protectedKeys[t]=!0:null!=e?k(t):d.add(t)}y.prevProp=g,y.prevResolvedValues=S,y.isActive&&(c={...c,...S}),s&&t.blockInitialAnimation&&(b=!1);let E=!(T&&w)||P;b&&E&&u.push(...A.map(t=>({animation:t,options:{type:o}})))}if(d.size){let e={};if("boolean"!=typeof l.initial){let i=o(t,Array.isArray(l.initial)?l.initial[0]:l.initial);i&&i.transition&&(e.transition=i.transition)}d.forEach(i=>{let s=t.getBaseTarget(i),n=t.getValue(i);n&&(n.liveStyle=!0),e[i]=s??null}),u.push({animation:e})}let y=!!u.length;return s&&(!1===l.initial||l.initial===l.animate)&&!t.manuallyAnimateOnMount&&(y=!1),s=!1,y?e(u):Promise.resolve()}return{animateChanges:a,setActive:function(e,s){if(i[e].isActive===s)return Promise.resolve();t.variantChildren?.forEach(t=>t.animationState?.setActive(e,s)),i[e].isActive=s;let n=a(e);for(let t in i)i[t].protectedKeys={};return n},setAnimateFunction:function(i){e=i(t)},getState:()=>i,reset:()=>{i=il(),s=!0}}}(t))}updateAnimationControlsSubscription(){let{animate:t}=this.node.getProps();n(t)&&(this.unmountControls=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:t}=this.node.getProps(),{animate:e}=this.node.prevProps||{};t!==e&&this.updateAnimationControlsSubscription()}unmount(){this.node.animationState.reset(),this.unmountControls?.()}}let id=0;class ic extends ih{constructor(){super(...arguments),this.id=id++}update(){if(!this.node.presenceContext)return;let{isPresent:t,onExitComplete:e}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;let s=this.node.animationState.setActive("exit",!t);e&&!t&&s.then(()=>{e(this.id)})}mount(){let{register:t,onExitComplete:e}=this.node.presenceContext||{};e&&e(this.id),t&&(this.unmount=t(this.id))}unmount(){}}let ip={x:!1,y:!1};function im(t,e,i,s={passive:!0}){return t.addEventListener(e,i,s),()=>t.removeEventListener(e,i)}let iy=t=>"mouse"===t.pointerType?"number"!=typeof t.button||t.button<=0:!1!==t.isPrimary;function ig(t){return{point:{x:t.pageX,y:t.pageY}}}let iv=t=>e=>iy(e)&&t(e,ig(e));function ix(t,e,i,s){return im(t,e,iv(i),s)}function iT({top:t,left:e,right:i,bottom:s}){return{x:{min:e,max:i},y:{min:t,max:s}}}function iw(t){return t.max-t.min}function ib(t,e,i,s=.5){t.origin=s,t.originPoint=tM(e.min,e.max,t.origin),t.scale=iw(i)/iw(e),t.translate=tM(i.min,i.max,t.origin)-t.originPoint,(t.scale>=.9999&&t.scale<=1.0001||isNaN(t.scale))&&(t.scale=1),(t.translate>=-.01&&t.translate<=.01||isNaN(t.translate))&&(t.translate=0)}function iP(t,e,i,s){ib(t.x,e.x,i.x,s?s.originX:void 0),ib(t.y,e.y,i.y,s?s.originY:void 0)}function iA(t,e,i){t.min=i.min+e.min,t.max=t.min+iw(e)}function iS(t,e,i){t.min=e.min-i.min,t.max=t.min+iw(e)}function iM(t,e,i){iS(t.x,e.x,i.x),iS(t.y,e.y,i.y)}let iV=()=>({translate:0,scale:1,origin:0,originPoint:0}),ik=()=>({x:iV(),y:iV()}),iE=()=>({min:0,max:0}),iD=()=>({x:iE(),y:iE()});function iC(t){return[t("x"),t("y")]}function iR(t){return void 0===t||1===t}function ij({scale:t,scaleX:e,scaleY:i}){return!iR(t)||!iR(e)||!iR(i)}function iL(t){return ij(t)||iF(t)||t.z||t.rotate||t.rotateX||t.rotateY||t.skewX||t.skewY}function iF(t){var e,i;return(e=t.x)&&"0%"!==e||(i=t.y)&&"0%"!==i}function iB(t,e,i,s,n){return void 0!==n&&(t=s+n*(t-s)),s+i*(t-s)+e}function iO(t,e=0,i=1,s,n){t.min=iB(t.min,e,i,s,n),t.max=iB(t.max,e,i,s,n)}function iI(t,{x:e,y:i}){iO(t.x,e.translate,e.scale,e.originPoint),iO(t.y,i.translate,i.scale,i.originPoint)}function iU(t,e){t.min=t.min+e,t.max=t.max+e}function iN(t,e,i,s,n=.5){let r=tM(t.min,t.max,n);iO(t,e,i,r,s)}function i$(t,e){iN(t.x,e.x,e.scaleX,e.scale,e.originX),iN(t.y,e.y,e.scaleY,e.scale,e.originY)}function iW(t,e){return iT(function(t,e){if(!e)return t;let i=e({x:t.left,y:t.top}),s=e({x:t.right,y:t.bottom});return{top:i.y,left:i.x,bottom:s.y,right:s.x}}(t.getBoundingClientRect(),e))}let iz=({current:t})=>t?t.ownerDocument.defaultView:null;function iY(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}let iH=(t,e)=>Math.abs(t-e);class iX{constructor(t,e,{transformPagePoint:i,contextWindow:s,dragSnapToOrigin:n=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let t=i_(this.lastMoveEventInfo,this.history),e=null!==this.startEvent,i=function(t,e){return Math.sqrt(iH(t.x,e.x)**2+iH(t.y,e.y)**2)}(t.offset,{x:0,y:0})>=3;if(!e&&!i)return;let{point:s}=t,{timestamp:n}=y;this.history.push({...s,timestamp:n});let{onStart:r,onMove:a}=this.handlers;e||(r&&r(this.lastMoveEvent,t),this.startEvent=this.lastMoveEvent),a&&a(this.lastMoveEvent,t)},this.handlePointerMove=(t,e)=>{this.lastMoveEvent=t,this.lastMoveEventInfo=iq(e,this.transformPagePoint),m.update(this.updatePoint,!0)},this.handlePointerUp=(t,e)=>{this.end();let{onEnd:i,onSessionEnd:s,resumeAnimation:n}=this.handlers;if(this.dragSnapToOrigin&&n&&n(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let r=i_("pointercancel"===t.type?this.lastMoveEventInfo:iq(e,this.transformPagePoint),this.history);this.startEvent&&i&&i(t,r),s&&s(t,r)},!iy(t))return;this.dragSnapToOrigin=n,this.handlers=e,this.transformPagePoint=i,this.contextWindow=s||window;let r=iq(ig(t),this.transformPagePoint),{point:a}=r,{timestamp:o}=y;this.history=[{...a,timestamp:o}];let{onSessionStart:l}=e;l&&l(t,i_(r,this.history)),this.removeListeners=B(ix(this.contextWindow,"pointermove",this.handlePointerMove),ix(this.contextWindow,"pointerup",this.handlePointerUp),ix(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),f(this.updatePoint)}}function iq(t,e){return e?{point:e(t.point)}:t}function iK(t,e){return{x:t.x-e.x,y:t.y-e.y}}function i_({point:t},e){return{point:t,delta:iK(t,iG(e)),offset:iK(t,e[0]),velocity:function(t,e){if(t.length<2)return{x:0,y:0};let i=t.length-1,s=null,n=iG(t);for(;i>=0&&(s=t[i],!(n.timestamp-s.timestamp>I(.1)));)i--;if(!s)return{x:0,y:0};let r=U(n.timestamp-s.timestamp);if(0===r)return{x:0,y:0};let a={x:(n.x-s.x)/r,y:(n.y-s.y)/r};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}(e,.1)}}function iG(t){return t[t.length-1]}function iZ(t,e,i){return{min:void 0!==e?t.min+e:void 0,max:void 0!==i?t.max+i-(t.max-t.min):void 0}}function iJ(t,e){let i=e.min-t.min,s=e.max-t.max;return e.max-e.min<t.max-t.min&&([i,s]=[s,i]),{min:i,max:s}}function iQ(t,e,i){return{min:i0(t,e),max:i0(t,i)}}function i0(t,e){return"number"==typeof t?t:t[e]||0}let i1=new WeakMap;class i5{constructor(t){this.openDragLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=iD(),this.visualElement=t}start(t,{snapToCursor:e=!1}={}){let{presenceContext:i}=this.visualElement;if(i&&!1===i.isPresent)return;let{dragSnapToOrigin:s}=this.getProps();this.panSession=new iX(t,{onSessionStart:t=>{let{dragSnapToOrigin:i}=this.getProps();i?this.pauseAnimation():this.stopAnimation(),e&&this.snapToCursor(ig(t).point)},onStart:(t,e)=>{let{drag:i,dragPropagation:s,onDragStart:n}=this.getProps();if(i&&!s&&(this.openDragLock&&this.openDragLock(),this.openDragLock=function(t){if("x"===t||"y"===t)if(ip[t])return null;else return ip[t]=!0,()=>{ip[t]=!1};return ip.x||ip.y?null:(ip.x=ip.y=!0,()=>{ip.x=ip.y=!1})}(i),!this.openDragLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),iC(t=>{let e=this.getAxisMotionValue(t).get()||0;if(tl.test(e)){let{projection:i}=this.visualElement;if(i&&i.layout){let s=i.layout.layoutBox[t];s&&(e=iw(s)*(parseFloat(e)/100))}}this.originPoint[t]=e}),n&&m.postRender(()=>n(t,e)),R(this.visualElement,"transform");let{animationState:r}=this.visualElement;r&&r.setActive("whileDrag",!0)},onMove:(t,e)=>{let{dragPropagation:i,dragDirectionLock:s,onDirectionLock:n,onDrag:r}=this.getProps();if(!i&&!this.openDragLock)return;let{offset:a}=e;if(s&&null===this.currentDirection){this.currentDirection=function(t,e=10){let i=null;return Math.abs(t.y)>e?i="y":Math.abs(t.x)>e&&(i="x"),i}(a),null!==this.currentDirection&&n&&n(this.currentDirection);return}this.updateAxis("x",e.point,a),this.updateAxis("y",e.point,a),this.visualElement.render(),r&&r(t,e)},onSessionEnd:(t,e)=>this.stop(t,e),resumeAnimation:()=>iC(t=>"paused"===this.getAnimationState(t)&&this.getAxisMotionValue(t).animation?.play())},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:s,contextWindow:iz(this.visualElement)})}stop(t,e){let i=this.isDragging;if(this.cancel(),!i)return;let{velocity:s}=e;this.startAnimation(s);let{onDragEnd:n}=this.getProps();n&&m.postRender(()=>n(t,e))}cancel(){this.isDragging=!1;let{projection:t,animationState:e}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:i}=this.getProps();!i&&this.openDragLock&&(this.openDragLock(),this.openDragLock=null),e&&e.setActive("whileDrag",!1)}updateAxis(t,e,i){let{drag:s}=this.getProps();if(!i||!i2(t,s,this.currentDirection))return;let n=this.getAxisMotionValue(t),r=this.originPoint[t]+i[t];this.constraints&&this.constraints[t]&&(r=function(t,{min:e,max:i},s){return void 0!==e&&t<e?t=s?tM(e,t,s.min):Math.max(t,e):void 0!==i&&t>i&&(t=s?tM(i,t,s.max):Math.min(t,i)),t}(r,this.constraints[t],this.elastic[t])),n.set(r)}resolveConstraints(){let{dragConstraints:t,dragElastic:e}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):this.visualElement.projection?.layout,s=this.constraints;t&&iY(t)?this.constraints||(this.constraints=this.resolveRefConstraints()):t&&i?this.constraints=function(t,{top:e,left:i,bottom:s,right:n}){return{x:iZ(t.x,i,n),y:iZ(t.y,e,s)}}(i.layoutBox,t):this.constraints=!1,this.elastic=function(t=.35){return!1===t?t=0:!0===t&&(t=.35),{x:iQ(t,"left","right"),y:iQ(t,"top","bottom")}}(e),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&iC(t=>{!1!==this.constraints&&this.getAxisMotionValue(t)&&(this.constraints[t]=function(t,e){let i={};return void 0!==e.min&&(i.min=e.min-t.min),void 0!==e.max&&(i.max=e.max-t.min),i}(i.layoutBox[t],this.constraints[t]))})}resolveRefConstraints(){var t;let{dragConstraints:e,onMeasureDragConstraints:i}=this.getProps();if(!e||!iY(e))return!1;let s=e.current;W(null!==s,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let r=function(t,e,i){let s=iW(t,i),{scroll:n}=e;return n&&(iU(s.x,n.offset.x),iU(s.y,n.offset.y)),s}(s,n.root,this.visualElement.getTransformPagePoint()),a=(t=n.layout.layoutBox,{x:iJ(t.x,r.x),y:iJ(t.y,r.y)});if(i){let t=i(function({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}(a));this.hasMutatedConstraints=!!t,t&&(a=iT(t))}return a}startAnimation(t){let{drag:e,dragMomentum:i,dragElastic:s,dragTransition:n,dragSnapToOrigin:r,onDragTransitionEnd:a}=this.getProps(),o=this.constraints||{};return Promise.all(iC(a=>{if(!i2(a,e,this.currentDirection))return;let l=o&&o[a]||{};r&&(l={min:0,max:0});let h={type:"inertia",velocity:i?t[a]:0,bounceStiffness:s?200:1e6,bounceDamping:s?40:1e7,timeConstant:750,restDelta:1,restSpeed:10,...n,...l};return this.startAxisValueAnimation(a,h)})).then(a)}startAxisValueAnimation(t,e){let i=this.getAxisMotionValue(t);return R(this.visualElement,t),i.start(e4(t,i,0,e,this.visualElement,!1))}stopAnimation(){iC(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){iC(t=>this.getAxisMotionValue(t).animation?.pause())}getAnimationState(t){return this.getAxisMotionValue(t).animation?.state}getAxisMotionValue(t){let e=`_drag${t.toUpperCase()}`,i=this.visualElement.getProps();return i[e]||this.visualElement.getValue(t,(i.initial?i.initial[t]:void 0)||0)}snapToCursor(t){iC(e=>{let{drag:i}=this.getProps();if(!i2(e,i,this.currentDirection))return;let{projection:s}=this.visualElement,n=this.getAxisMotionValue(e);if(s&&s.layout){let{min:i,max:r}=s.layout.layoutBox[e];n.set(t[e]-tM(i,r,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:t,dragConstraints:e}=this.getProps(),{projection:i}=this.visualElement;if(!iY(e)||!i||!this.constraints)return;this.stopAnimation();let s={x:0,y:0};iC(t=>{let e=this.getAxisMotionValue(t);if(e&&!1!==this.constraints){let i=e.get();s[t]=function(t,e){let i=.5,s=iw(t),n=iw(e);return n>s?i=er(e.min,e.max-s,t.min):s>n&&(i=er(t.min,t.max-n,e.min)),O(0,1,i)}({min:i,max:i},this.constraints[t])}});let{transformTemplate:n}=this.visualElement.getProps();this.visualElement.current.style.transform=n?n({},""):"none",i.root&&i.root.updateScroll(),i.updateLayout(),this.resolveConstraints(),iC(e=>{if(!i2(e,t,null))return;let i=this.getAxisMotionValue(e),{min:n,max:r}=this.constraints[e];i.set(tM(n,r,s[e]))})}addListeners(){if(!this.visualElement.current)return;i1.set(this.visualElement,this);let t=ix(this.visualElement.current,"pointerdown",t=>{let{drag:e,dragListener:i=!0}=this.getProps();e&&i&&this.start(t)}),e=()=>{let{dragConstraints:t}=this.getProps();iY(t)&&t.current&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",e);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),m.read(e);let n=im(window,"resize",()=>this.scalePositionWithinConstraints()),r=i.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e})=>{this.isDragging&&e&&(iC(e=>{let i=this.getAxisMotionValue(e);i&&(this.originPoint[e]+=t[e].translate,i.set(i.get()+t[e].translate))}),this.visualElement.render())});return()=>{n(),t(),s(),r&&r()}}getProps(){let t=this.visualElement.getProps(),{drag:e=!1,dragDirectionLock:i=!1,dragPropagation:s=!1,dragConstraints:n=!1,dragElastic:r=.35,dragMomentum:a=!0}=t;return{...t,drag:e,dragDirectionLock:i,dragPropagation:s,dragConstraints:n,dragElastic:r,dragMomentum:a}}}function i2(t,e,i){return(!0===e||e===t)&&(null===i||i===t)}class i3 extends ih{constructor(t){super(t),this.removeGroupControls=h,this.removeListeners=h,this.controls=new i5(t)}mount(){let{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||h}unmount(){this.removeGroupControls(),this.removeListeners()}}let i4=t=>(e,i)=>{t&&m.postRender(()=>t(e,i))};class i9 extends ih{constructor(){super(...arguments),this.removePointerDownListener=h}onPointerDown(t){this.session=new iX(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:iz(this.node)})}createPanHandlers(){let{onPanSessionStart:t,onPanStart:e,onPan:i,onPanEnd:s}=this.node.getProps();return{onSessionStart:i4(t),onStart:i4(e),onMove:i,onEnd:(t,e)=>{delete this.session,s&&m.postRender(()=>s(t,e))}}}mount(){this.removePointerDownListener=ix(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}var i6,i8,i7=i(95155);let{schedule:st}=p(queueMicrotask,!1);var se=i(12115);let si=(0,se.createContext)(null),ss=(0,se.createContext)({}),sn=(0,se.createContext)({}),sr={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function sa(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}let so={correct:(t,e)=>{if(!e.target)return t;if("string"==typeof t)if(!th.test(t))return t;else t=parseFloat(t);let i=sa(t,e.target.x),s=sa(t,e.target.y);return`${i}% ${s}%`}},sl={};class sh extends se.Component{componentDidMount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i,layoutId:s}=this.props,{projection:n}=t;for(let t in sd)sl[t]=sd[t],Y(t)&&(sl[t].isCSSVariable=!0);n&&(e.group&&e.group.add(n),i&&i.register&&s&&i.register(n),n.root.didUpdate(),n.addEventListener("animationComplete",()=>{this.safeToRemove()}),n.setOptions({...n.options,onExitComplete:()=>this.safeToRemove()})),sr.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){let{layoutDependency:e,visualElement:i,drag:s,isPresent:n}=this.props,{projection:r}=i;return r&&(r.isPresent=n,s||t.layoutDependency!==e||void 0===e||t.isPresent!==n?r.willUpdate():this.safeToRemove(),t.isPresent!==n&&(n?r.promote():r.relegate()||m.postRender(()=>{let t=r.getStack();t&&t.members.length||this.safeToRemove()}))),null}componentDidUpdate(){let{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),st.postRender(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){let{visualElement:t,layoutGroup:e,switchLayoutGroup:i}=this.props,{projection:s}=t;s&&(s.scheduleCheckAfterUnmount(),e&&e.group&&e.group.remove(s),i&&i.deregister&&i.deregister(s))}safeToRemove(){let{safeToRemove:t}=this.props;t&&t()}render(){return null}}function su(t){let[e,i]=function(t=!0){let e=(0,se.useContext)(si);if(null===e)return[!0,null];let{isPresent:i,onExitComplete:s,register:n}=e,r=(0,se.useId)();(0,se.useEffect)(()=>{if(t)return n(r)},[t]);let a=(0,se.useCallback)(()=>t&&s&&s(r),[r,s,t]);return!i&&s?[!1,a]:[!0]}(),s=(0,se.useContext)(ss);return(0,i7.jsx)(sh,{...t,layoutGroup:s,switchLayoutGroup:(0,se.useContext)(sn),isPresent:e,safeToRemove:i})}let sd={borderRadius:{...so,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:so,borderTopRightRadius:so,borderBottomLeftRadius:so,borderBottomRightRadius:so,boxShadow:{correct:(t,{treeScale:e,projectionDelta:i})=>{let s=tP.parse(t);if(s.length>5)return t;let n=tP.createTransformer(t),r=+("number"!=typeof s[0]),a=i.x.scale*e.x,o=i.y.scale*e.y;s[0+r]/=a,s[1+r]/=o;let l=tM(a,o,.5);return"number"==typeof s[2+r]&&(s[2+r]/=l),"number"==typeof s[3+r]&&(s[3+r]/=l),n(s)}}};function sc(t){return eK(t)&&"ownerSVGElement"in t}let sp=(t,e)=>t.depth-e.depth;class sm{constructor(){this.children=[],this.isDirty=!1}add(t){w(this.children,t),this.isDirty=!0}remove(t){b(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(sp),this.isDirty=!1,this.children.forEach(t)}}function sf(t){return C(t)?t.get():t}let sy=["TopLeft","TopRight","BottomLeft","BottomRight"],sg=sy.length,sv=t=>"string"==typeof t?parseFloat(t):t,sx=t=>"number"==typeof t||th.test(t);function sT(t,e){return void 0!==t[e]?t[e]:t.borderRadius}let sw=sP(0,.5,t7),sb=sP(.5,.95,h);function sP(t,e,i){return s=>s<t?0:s>e?1:i(er(t,e,s))}function sA(t,e){t.min=e.min,t.max=e.max}function sS(t,e){sA(t.x,e.x),sA(t.y,e.y)}function sM(t,e){t.translate=e.translate,t.scale=e.scale,t.originPoint=e.originPoint,t.origin=e.origin}function sV(t,e,i,s,n){return t-=e,t=s+1/i*(t-s),void 0!==n&&(t=s+1/n*(t-s)),t}function sk(t,e,[i,s,n],r,a){!function(t,e=0,i=1,s=.5,n,r=t,a=t){if(tl.test(e)&&(e=parseFloat(e),e=tM(a.min,a.max,e/100)-a.min),"number"!=typeof e)return;let o=tM(r.min,r.max,s);t===r&&(o-=e),t.min=sV(t.min,e,i,o,n),t.max=sV(t.max,e,i,o,n)}(t,e[i],e[s],e[n],e.scale,r,a)}let sE=["x","scaleX","originX"],sD=["y","scaleY","originY"];function sC(t,e,i,s){sk(t.x,e,sE,i?i.x:void 0,s?s.x:void 0),sk(t.y,e,sD,i?i.y:void 0,s?s.y:void 0)}function sR(t){return 0===t.translate&&1===t.scale}function sj(t){return sR(t.x)&&sR(t.y)}function sL(t,e){return t.min===e.min&&t.max===e.max}function sF(t,e){return Math.round(t.min)===Math.round(e.min)&&Math.round(t.max)===Math.round(e.max)}function sB(t,e){return sF(t.x,e.x)&&sF(t.y,e.y)}function sO(t){return iw(t.x)/iw(t.y)}function sI(t,e){return t.translate===e.translate&&t.scale===e.scale&&t.originPoint===e.originPoint}class sU{constructor(){this.members=[]}add(t){w(this.members,t),t.scheduleRender()}remove(t){if(b(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){let t=this.members[this.members.length-1];t&&this.promote(t)}}relegate(t){let e,i=this.members.findIndex(e=>t===e);if(0===i)return!1;for(let t=i;t>=0;t--){let i=this.members[t];if(!1!==i.isPresent){e=i;break}}return!!e&&(this.promote(e),!0)}promote(t,e){let i=this.lead;if(t!==i&&(this.prevLead=i,this.lead=t,t.show(),i)){i.instance&&i.scheduleRender(),t.scheduleRender(),t.resumeFrom=i,e&&(t.resumeFrom.preserveOpacity=!0),i.snapshot&&(t.snapshot=i.snapshot,t.snapshot.latestValues=i.animationValues||i.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);let{crossfade:s}=t.options;!1===s&&i.hide()}}exitAnimationComplete(){this.members.forEach(t=>{let{options:e,resumingFrom:i}=t;e.onExitComplete&&e.onExitComplete(),i&&i.options.onExitComplete&&i.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}let sN={nodes:0,calculatedTargetDeltas:0,calculatedProjections:0},s$=["","X","Y","Z"],sW={visibility:"hidden"},sz=0;function sY(t,e,i,s){let{latestValues:n}=e;n[t]&&(i[t]=n[t],e.setStaticValue(t,0),s&&(s[t]=0))}function sH({attachResizeListener:t,defaultParent:e,measureScroll:i,checkIsScrollRoot:s,resetTransform:n}){return class{constructor(t={},i=e?.()){this.id=sz++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.hasCheckedOptimisedAppear=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.scheduleUpdate=()=>this.update(),this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,c.value&&(sN.nodes=sN.calculatedTargetDeltas=sN.calculatedProjections=0),this.nodes.forEach(sK),this.nodes.forEach(s1),this.nodes.forEach(s5),this.nodes.forEach(s_),c.addProjectionMetrics&&c.addProjectionMetrics(sN)},this.resolvedRelativeTargetAt=0,this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=t,this.root=i?i.root||i:this,this.path=i?[...i.path,i]:[],this.parent=i,this.depth=i?i.depth+1:0;for(let t=0;t<this.path.length;t++)this.path[t].shouldResetTransform=!0;this.root===this&&(this.nodes=new sm)}addEventListener(t,e){return this.eventHandlers.has(t)||this.eventHandlers.set(t,new P),this.eventHandlers.get(t).add(e)}notifyListeners(t,...e){let i=this.eventHandlers.get(t);i&&i.notify(...e)}hasListeners(t){return this.eventHandlers.has(t)}mount(e){if(this.instance)return;this.isSVG=sc(e)&&!(sc(e)&&"svg"===e.tagName),this.instance=e;let{layoutId:i,layout:s,visualElement:n}=this.options;if(n&&!n.current&&n.mount(e),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.root.hasTreeAnimated&&(s||i)&&(this.isLayoutDirty=!0),t){let i,s=()=>this.root.updateBlockedByResize=!1;t(e,()=>{this.root.updateBlockedByResize=!0,i&&i(),i=function(t,e){let i=S.now(),s=({timestamp:n})=>{let r=n-i;r>=250&&(f(s),t(r-e))};return m.setup(s,!0),()=>f(s)}(s,250),sr.hasAnimatedSinceResize&&(sr.hasAnimatedSinceResize=!1,this.nodes.forEach(s0))})}i&&this.root.registerSharedNode(i,this),!1!==this.options.animate&&n&&(i||s)&&this.addEventListener("didUpdate",({delta:t,hasLayoutChanged:e,hasRelativeLayoutChanged:i,layout:s})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let r=this.options.transition||n.getDefaultTransition()||s8,{onLayoutAnimationStart:a,onLayoutAnimationComplete:o}=n.getProps(),h=!this.targetLayout||!sB(this.targetLayout,s),u=!e&&i;if(this.options.layoutRoot||this.resumeFrom||u||e&&(h||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0);let e={...l(r,"layout"),onPlay:a,onComplete:o};(n.shouldReduceMotion||this.options.layoutRoot)&&(e.delay=0,e.type=!1),this.startAnimation(e),this.setAnimationOrigin(t,u)}else e||s0(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=s})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let t=this.getStack();t&&t.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,this.eventHandlers.clear(),f(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){!this.isUpdateBlocked()&&(this.isUpdating=!0,this.nodes&&this.nodes.forEach(s2),this.animationId++)}getTransformTemplate(){let{visualElement:t}=this.options;return t&&t.getProps().transformTemplate}willUpdate(t=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(window.MotionCancelOptimisedAnimation&&!this.hasCheckedOptimisedAppear&&function t(e){if(e.hasCheckedOptimisedAppear=!0,e.root===e)return;let{visualElement:i}=e.options;if(!i)return;let s=i.props[L];if(window.MotionHasOptimisedAnimation(s,"transform")){let{layout:t,layoutId:i}=e.options;window.MotionCancelOptimisedAnimation(s,"transform",m,!(t||i))}let{parent:n}=e;n&&!n.hasCheckedOptimisedAppear&&t(n)}(this),this.root.isUpdating||this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let t=0;t<this.path.length;t++){let e=this.path[t];e.shouldResetTransform=!0,e.updateScroll("snapshot"),e.options.layoutRoot&&e.willUpdate(!1)}let{layoutId:e,layout:i}=this.options;if(void 0===e&&!i)return;let s=this.getTransformTemplate();this.prevTransformTemplateValue=s?s(this.latestValues,""):void 0,this.updateSnapshot(),t&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(sZ);return}this.isUpdating||this.nodes.forEach(sJ),this.isUpdating=!1,this.nodes.forEach(sQ),this.nodes.forEach(sX),this.nodes.forEach(sq),this.clearAllSnapshots();let t=S.now();y.delta=O(0,1e3/60,t-y.timestamp),y.timestamp=t,y.isProcessing=!0,g.update.process(y),g.preRender.process(y),g.render.process(y),y.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,st.read(this.scheduleUpdate))}clearAllSnapshots(){this.nodes.forEach(sG),this.sharedNodes.forEach(s3)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,m.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){m.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){!this.snapshot&&this.instance&&(this.snapshot=this.measure(),!this.snapshot||iw(this.snapshot.measuredBox.x)||iw(this.snapshot.measuredBox.y)||(this.snapshot=void 0))}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let t=0;t<this.path.length;t++)this.path[t].updateScroll();let t=this.layout;this.layout=this.measure(!1),this.layoutCorrected=iD(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:e}=this.options;e&&e.notify("LayoutMeasure",this.layout.layoutBox,t?t.layoutBox:void 0)}updateScroll(t="measure"){let e=!!(this.options.layoutScroll&&this.instance);if(this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===t&&(e=!1),e&&this.instance){let e=s(this.instance);this.scroll={animationId:this.root.animationId,phase:t,isRoot:e,offset:i(this.instance),wasRoot:this.scroll?this.scroll.isRoot:e}}}resetTransform(){if(!n)return;let t=this.isLayoutDirty||this.shouldResetTransform||this.options.alwaysMeasureLayout,e=this.projectionDelta&&!sj(this.projectionDelta),i=this.getTransformTemplate(),s=i?i(this.latestValues,""):void 0,r=s!==this.prevTransformTemplateValue;t&&this.instance&&(e||iL(this.latestValues)||r)&&(n(this.instance,s),this.shouldResetTransform=!1,this.scheduleRender())}measure(t=!0){var e;let i=this.measurePageBox(),s=this.removeElementScroll(i);return t&&(s=this.removeTransform(s)),ne((e=s).x),ne(e.y),{animationId:this.root.animationId,measuredBox:i,layoutBox:s,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:t}=this.options;if(!t)return iD();let e=t.measureViewportBox();if(!(this.scroll?.wasRoot||this.path.some(ns))){let{scroll:t}=this.root;t&&(iU(e.x,t.offset.x),iU(e.y,t.offset.y))}return e}removeElementScroll(t){let e=iD();if(sS(e,t),this.scroll?.wasRoot)return e;for(let i=0;i<this.path.length;i++){let s=this.path[i],{scroll:n,options:r}=s;s!==this.root&&n&&r.layoutScroll&&(n.wasRoot&&sS(e,t),iU(e.x,n.offset.x),iU(e.y,n.offset.y))}return e}applyTransform(t,e=!1){let i=iD();sS(i,t);for(let t=0;t<this.path.length;t++){let s=this.path[t];!e&&s.options.layoutScroll&&s.scroll&&s!==s.root&&i$(i,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),iL(s.latestValues)&&i$(i,s.latestValues)}return iL(this.latestValues)&&i$(i,this.latestValues),i}removeTransform(t){let e=iD();sS(e,t);for(let t=0;t<this.path.length;t++){let i=this.path[t];if(!i.instance||!iL(i.latestValues))continue;ij(i.latestValues)&&i.updateSnapshot();let s=iD();sS(s,i.measurePageBox()),sC(e,i.latestValues,i.snapshot?i.snapshot.layoutBox:void 0,s)}return iL(this.latestValues)&&sC(e,this.latestValues),e}setTargetDelta(t){this.targetDelta=t,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(t){this.options={...this.options,...t,crossfade:void 0===t.crossfade||t.crossfade}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==y.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(t=!1){let e=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=e.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=e.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=e.isSharedProjectionDirty);let i=!!this.resumingFrom||this!==e;if(!(t||i&&this.isSharedProjectionDirty||this.isProjectionDirty||this.parent?.isProjectionDirty||this.attemptToResolveRelativeTarget||this.root.updateBlockedByResize))return;let{layout:s,layoutId:n}=this.options;if(this.layout&&(s||n)){if(this.resolvedRelativeTargetAt=y.timestamp,!this.targetDelta&&!this.relativeTarget){let t=this.getClosestProjectingParent();t&&t.layout&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.layout.layoutBox,t.layout.layoutBox),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(this.relativeTarget||this.targetDelta){if(this.target||(this.target=iD(),this.targetWithTransforms=iD()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target){var r,a,o;this.forceRelativeParentToResolveTarget(),r=this.target,a=this.relativeTarget,o=this.relativeParent.target,iA(r.x,a.x,o.x),iA(r.y,a.y,o.y)}else this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):sS(this.target,this.layout.layoutBox),iI(this.target,this.targetDelta)):sS(this.target,this.layout.layoutBox);if(this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let t=this.getClosestProjectingParent();t&&!!t.resumingFrom==!!this.resumingFrom&&!t.options.layoutScroll&&t.target&&1!==this.animationProgress?(this.relativeParent=t,this.forceRelativeParentToResolveTarget(),this.relativeTarget=iD(),this.relativeTargetOrigin=iD(),iM(this.relativeTargetOrigin,this.target,t.target),sS(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}c.value&&sN.calculatedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ij(this.parent.latestValues)||iF(this.parent.latestValues)))if(this.parent.isProjecting())return this.parent;else return this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){let t=this.getLead(),e=!!this.resumingFrom||this!==t,i=!0;if((this.isProjectionDirty||this.parent?.isProjectionDirty)&&(i=!1),e&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(i=!1),this.resolvedRelativeTargetAt===y.timestamp&&(i=!1),i)return;let{layout:s,layoutId:n}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(s||n))return;sS(this.layoutCorrected,this.layout.layoutBox);let r=this.treeScale.x,a=this.treeScale.y;!function(t,e,i,s=!1){let n,r,a=i.length;if(a){e.x=e.y=1;for(let o=0;o<a;o++){r=(n=i[o]).projectionDelta;let{visualElement:a}=n.options;(!a||!a.props.style||"contents"!==a.props.style.display)&&(s&&n.options.layoutScroll&&n.scroll&&n!==n.root&&i$(t,{x:-n.scroll.offset.x,y:-n.scroll.offset.y}),r&&(e.x*=r.x.scale,e.y*=r.y.scale,iI(t,r)),s&&iL(n.latestValues)&&i$(t,n.latestValues))}e.x<1.0000000000001&&e.x>.999999999999&&(e.x=1),e.y<1.0000000000001&&e.y>.999999999999&&(e.y=1)}}(this.layoutCorrected,this.treeScale,this.path,e),t.layout&&!t.target&&(1!==this.treeScale.x||1!==this.treeScale.y)&&(t.target=t.layout.layoutBox,t.targetWithTransforms=iD());let{target:o}=t;if(!o){this.prevProjectionDelta&&(this.createProjectionDeltas(),this.scheduleRender());return}this.projectionDelta&&this.prevProjectionDelta?(sM(this.prevProjectionDelta.x,this.projectionDelta.x),sM(this.prevProjectionDelta.y,this.projectionDelta.y)):this.createProjectionDeltas(),iP(this.projectionDelta,this.layoutCorrected,o,this.latestValues),this.treeScale.x===r&&this.treeScale.y===a&&sI(this.projectionDelta.x,this.prevProjectionDelta.x)&&sI(this.projectionDelta.y,this.prevProjectionDelta.y)||(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",o)),c.value&&sN.calculatedProjections++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(t=!0){if(this.options.visualElement?.scheduleRender(),t){let t=this.getStack();t&&t.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}createProjectionDeltas(){this.prevProjectionDelta=ik(),this.projectionDelta=ik(),this.projectionDeltaWithTransform=ik()}setAnimationOrigin(t,e=!1){let i,s=this.snapshot,n=s?s.latestValues:{},r={...this.latestValues},a=ik();this.relativeParent&&this.relativeParent.options.layoutRoot||(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!e;let o=iD(),l=(s?s.source:void 0)!==(this.layout?this.layout.source:void 0),h=this.getStack(),u=!h||h.members.length<=1,d=!!(l&&!u&&!0===this.options.crossfade&&!this.path.some(s6));this.animationProgress=0,this.mixTargetDelta=e=>{let s=e/1e3;if(s4(a.x,t.x,s),s4(a.y,t.y,s),this.setTargetDelta(a),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout){var h,c,p,m,f,y;iM(o,this.layout.layoutBox,this.relativeParent.layout.layoutBox),p=this.relativeTarget,m=this.relativeTargetOrigin,f=o,y=s,s9(p.x,m.x,f.x,y),s9(p.y,m.y,f.y,y),i&&(h=this.relativeTarget,c=i,sL(h.x,c.x)&&sL(h.y,c.y))&&(this.isProjectionDirty=!1),i||(i=iD()),sS(i,this.relativeTarget)}l&&(this.animationValues=r,function(t,e,i,s,n,r){n?(t.opacity=tM(0,i.opacity??1,sw(s)),t.opacityExit=tM(e.opacity??1,0,sb(s))):r&&(t.opacity=tM(e.opacity??1,i.opacity??1,s));for(let n=0;n<sg;n++){let r=`border${sy[n]}Radius`,a=sT(e,r),o=sT(i,r);(void 0!==a||void 0!==o)&&(a||(a=0),o||(o=0),0===a||0===o||sx(a)===sx(o)?(t[r]=Math.max(tM(sv(a),sv(o),s),0),(tl.test(o)||tl.test(a))&&(t[r]+="%")):t[r]=o)}(e.rotate||i.rotate)&&(t.rotate=tM(e.rotate||0,i.rotate||0,s))}(r,n,this.latestValues,s,d,u)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=s},this.mixTargetDelta(1e3*!!this.options.layoutRoot)}startAnimation(t){this.notifyListeners("animationStart"),this.currentAnimation?.stop(),this.resumingFrom?.currentAnimation?.stop(),this.pendingAnimation&&(f(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=m.update(()=>{sr.hasAnimatedSinceResize=!0,N.layout++,this.motionValue||(this.motionValue=E(0)),this.currentAnimation=function(t,e,i){let s=C(t)?t:E(t);return s.start(e4("",s,e,i)),s.animation}(this.motionValue,[0,1e3],{...t,velocity:0,isSync:!0,onUpdate:e=>{this.mixTargetDelta(e),t.onUpdate&&t.onUpdate(e)},onStop:()=>{N.layout--},onComplete:()=>{N.layout--,t.onComplete&&t.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let t=this.getStack();t&&t.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(1e3),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let t=this.getLead(),{targetWithTransforms:e,target:i,layout:s,latestValues:n}=t;if(e&&i&&s){if(this!==t&&this.layout&&s&&ni(this.options.animationType,this.layout.layoutBox,s.layoutBox)){i=this.target||iD();let e=iw(this.layout.layoutBox.x);i.x.min=t.target.x.min,i.x.max=i.x.min+e;let s=iw(this.layout.layoutBox.y);i.y.min=t.target.y.min,i.y.max=i.y.min+s}sS(e,i),i$(e,n),iP(this.projectionDeltaWithTransform,this.layoutCorrected,e,n)}}registerSharedNode(t,e){this.sharedNodes.has(t)||this.sharedNodes.set(t,new sU),this.sharedNodes.get(t).add(e);let i=e.options.initialPromotionConfig;e.promote({transition:i?i.transition:void 0,preserveFollowOpacity:i&&i.shouldPreserveFollowOpacity?i.shouldPreserveFollowOpacity(e):void 0})}isLead(){let t=this.getStack();return!t||t.lead===this}getLead(){let{layoutId:t}=this.options;return t&&this.getStack()?.lead||this}getPrevLead(){let{layoutId:t}=this.options;return t?this.getStack()?.prevLead:void 0}getStack(){let{layoutId:t}=this.options;if(t)return this.root.sharedNodes.get(t)}promote({needsReset:t,transition:e,preserveFollowOpacity:i}={}){let s=this.getStack();s&&s.promote(this,i),t&&(this.projectionDelta=void 0,this.needsReset=!0),e&&this.setOptions({transition:e})}relegate(){let t=this.getStack();return!!t&&t.relegate(this)}resetSkewAndRotation(){let{visualElement:t}=this.options;if(!t)return;let e=!1,{latestValues:i}=t;if((i.z||i.rotate||i.rotateX||i.rotateY||i.rotateZ||i.skewX||i.skewY)&&(e=!0),!e)return;let s={};i.z&&sY("z",t,s,this.animationValues);for(let e=0;e<s$.length;e++)sY(`rotate${s$[e]}`,t,s,this.animationValues),sY(`skew${s$[e]}`,t,s,this.animationValues);for(let e in t.render(),s)t.setStaticValue(e,s[e]),this.animationValues&&(this.animationValues[e]=s[e]);t.scheduleRender()}getProjectionStyles(t){if(!this.instance||this.isSVG)return;if(!this.isVisible)return sW;let e={visibility:""},i=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,e.opacity="",e.pointerEvents=sf(t?.pointerEvents)||"",e.transform=i?i(this.latestValues,""):"none",e;let s=this.getLead();if(!this.projectionDelta||!this.layout||!s.target){let e={};return this.options.layoutId&&(e.opacity=void 0!==this.latestValues.opacity?this.latestValues.opacity:1,e.pointerEvents=sf(t?.pointerEvents)||""),this.hasProjected&&!iL(this.latestValues)&&(e.transform=i?i({},""):"none",this.hasProjected=!1),e}let n=s.animationValues||s.latestValues;this.applyTransformsToTarget(),e.transform=function(t,e,i){let s="",n=t.x.translate/e.x,r=t.y.translate/e.y,a=i?.z||0;if((n||r||a)&&(s=`translate3d(${n}px, ${r}px, ${a}px) `),(1!==e.x||1!==e.y)&&(s+=`scale(${1/e.x}, ${1/e.y}) `),i){let{transformPerspective:t,rotate:e,rotateX:n,rotateY:r,skewX:a,skewY:o}=i;t&&(s=`perspective(${t}px) ${s}`),e&&(s+=`rotate(${e}deg) `),n&&(s+=`rotateX(${n}deg) `),r&&(s+=`rotateY(${r}deg) `),a&&(s+=`skewX(${a}deg) `),o&&(s+=`skewY(${o}deg) `)}let o=t.x.scale*e.x,l=t.y.scale*e.y;return(1!==o||1!==l)&&(s+=`scale(${o}, ${l})`),s||"none"}(this.projectionDeltaWithTransform,this.treeScale,n),i&&(e.transform=i(n,e.transform));let{x:r,y:a}=this.projectionDelta;for(let t in e.transformOrigin=`${100*r.origin}% ${100*a.origin}% 0`,s.animationValues?e.opacity=s===this?n.opacity??this.latestValues.opacity??1:this.preserveOpacity?this.latestValues.opacity:n.opacityExit:e.opacity=s===this?void 0!==n.opacity?n.opacity:"":void 0!==n.opacityExit?n.opacityExit:0,sl){if(void 0===n[t])continue;let{correct:i,applyTo:r,isCSSVariable:a}=sl[t],o="none"===e.transform?n[t]:i(n[t],s);if(r){let t=r.length;for(let i=0;i<t;i++)e[r[i]]=o}else a?this.options.visualElement.renderState.vars[t]=o:e[t]=o}return this.options.layoutId&&(e.pointerEvents=s===this?sf(t?.pointerEvents)||"":"none"),e}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(t=>t.currentAnimation?.stop()),this.root.nodes.forEach(sZ),this.root.sharedNodes.clear()}}}function sX(t){t.updateLayout()}function sq(t){let e=t.resumeFrom?.snapshot||t.snapshot;if(t.isLead()&&t.layout&&e&&t.hasListeners("didUpdate")){let{layoutBox:i,measuredBox:s}=t.layout,{animationType:n}=t.options,r=e.source!==t.layout.source;"size"===n?iC(t=>{let s=r?e.measuredBox[t]:e.layoutBox[t],n=iw(s);s.min=i[t].min,s.max=s.min+n}):ni(n,e.layoutBox,i)&&iC(s=>{let n=r?e.measuredBox[s]:e.layoutBox[s],a=iw(i[s]);n.max=n.min+a,t.relativeTarget&&!t.currentAnimation&&(t.isProjectionDirty=!0,t.relativeTarget[s].max=t.relativeTarget[s].min+a)});let a=ik();iP(a,i,e.layoutBox);let o=ik();r?iP(o,t.applyTransform(s,!0),e.measuredBox):iP(o,i,e.layoutBox);let l=!sj(a),h=!1;if(!t.resumeFrom){let s=t.getClosestProjectingParent();if(s&&!s.resumeFrom){let{snapshot:n,layout:r}=s;if(n&&r){let a=iD();iM(a,e.layoutBox,n.layoutBox);let o=iD();iM(o,i,r.layoutBox),sB(a,o)||(h=!0),s.options.layoutRoot&&(t.relativeTarget=o,t.relativeTargetOrigin=a,t.relativeParent=s)}}}t.notifyListeners("didUpdate",{layout:i,snapshot:e,delta:o,layoutDelta:a,hasLayoutChanged:l,hasRelativeLayoutChanged:h})}else if(t.isLead()){let{onExitComplete:e}=t.options;e&&e()}t.options.transition=void 0}function sK(t){c.value&&sN.nodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function s_(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function sG(t){t.clearSnapshot()}function sZ(t){t.clearMeasurements()}function sJ(t){t.isLayoutDirty=!1}function sQ(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function s0(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0,t.isProjectionDirty=!0}function s1(t){t.resolveTargetDelta()}function s5(t){t.calcProjection()}function s2(t){t.resetSkewAndRotation()}function s3(t){t.removeLeadSnapshot()}function s4(t,e,i){t.translate=tM(e.translate,0,i),t.scale=tM(e.scale,1,i),t.origin=e.origin,t.originPoint=e.originPoint}function s9(t,e,i,s){t.min=tM(e.min,i.min,s),t.max=tM(e.max,i.max,s)}function s6(t){return t.animationValues&&void 0!==t.animationValues.opacityExit}let s8={duration:.45,ease:[.4,0,.1,1]},s7=t=>"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().includes(t),nt=s7("applewebkit/")&&!s7("chrome/")?Math.round:h;function ne(t){t.min=nt(t.min),t.max=nt(t.max)}function ni(t,e,i){return"position"===t||"preserve-aspect"===t&&!(.2>=Math.abs(sO(e)-sO(i)))}function ns(t){return t!==t.root&&t.scroll?.wasRoot}let nn=sH({attachResizeListener:(t,e)=>im(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),nr={current:void 0},na=sH({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!nr.current){let t=new nn({});t.mount(window),t.setOptions({layoutScroll:!0}),nr.current=t}return nr.current},resetTransform:(t,e)=>{t.style.transform=void 0!==e?e:"none"},checkIsScrollRoot:t=>"fixed"===window.getComputedStyle(t).position});function no(t,e){let i=function(t,e,i){if(t instanceof EventTarget)return[t];if("string"==typeof t){let e=document,i=(void 0)??e.querySelectorAll(t);return i?Array.from(i):[]}return Array.from(t)}(t),s=new AbortController;return[i,{passive:!0,...e,signal:s.signal},()=>s.abort()]}function nl(t){return!("touch"===t.pointerType||ip.x||ip.y)}function nh(t,e,i){let{props:s}=t;t.animationState&&s.whileHover&&t.animationState.setActive("whileHover","Start"===i);let n=s["onHover"+i];n&&m.postRender(()=>n(e,ig(e)))}class nu extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=no(t,i),a=t=>{if(!nl(t))return;let{target:i}=t,s=e(i,t);if("function"!=typeof s||!i)return;let r=t=>{nl(t)&&(s(t),i.removeEventListener("pointerleave",r))};i.addEventListener("pointerleave",r,n)};return s.forEach(t=>{t.addEventListener("pointerenter",a,n)}),r}(t,(t,e)=>(nh(this.node,e,"Start"),t=>nh(this.node,t,"End"))))}unmount(){}}class nd extends ih{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch(e){t=!0}t&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){this.isActive&&this.node.animationState&&(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=B(im(this.node.current,"focus",()=>this.onFocus()),im(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}let nc=(t,e)=>!!e&&(t===e||nc(t,e.parentElement)),np=new Set(["BUTTON","INPUT","SELECT","TEXTAREA","A"]),nm=new WeakSet;function nf(t){return e=>{"Enter"===e.key&&t(e)}}function ny(t,e){t.dispatchEvent(new PointerEvent("pointer"+e,{isPrimary:!0,bubbles:!0}))}let ng=(t,e)=>{let i=t.currentTarget;if(!i)return;let s=nf(()=>{if(nm.has(i))return;ny(i,"down");let t=nf(()=>{ny(i,"up")});i.addEventListener("keyup",t,e),i.addEventListener("blur",()=>ny(i,"cancel"),e)});i.addEventListener("keydown",s,e),i.addEventListener("blur",()=>i.removeEventListener("keydown",s),e)};function nv(t){return iy(t)&&!(ip.x||ip.y)}function nx(t,e,i){let{props:s}=t;if(t.current instanceof HTMLButtonElement&&t.current.disabled)return;t.animationState&&s.whileTap&&t.animationState.setActive("whileTap","Start"===i);let n=s["onTap"+("End"===i?"":i)];n&&m.postRender(()=>n(e,ig(e)))}class nT extends ih{mount(){let{current:t}=this.node;t&&(this.unmount=function(t,e,i={}){let[s,n,r]=no(t,i),a=t=>{let s=t.currentTarget;if(!nv(t))return;nm.add(s);let r=e(s,t),a=(t,e)=>{window.removeEventListener("pointerup",o),window.removeEventListener("pointercancel",l),nm.has(s)&&nm.delete(s),nv(t)&&"function"==typeof r&&r(t,{success:e})},o=t=>{a(t,s===window||s===document||i.useGlobalTarget||nc(s,t.target))},l=t=>{a(t,!1)};window.addEventListener("pointerup",o,n),window.addEventListener("pointercancel",l,n)};return s.forEach(t=>{((i.useGlobalTarget?window:t).addEventListener("pointerdown",a,n),e_(t))&&(t.addEventListener("focus",t=>ng(t,n)),np.has(t.tagName)||-1!==t.tabIndex||t.hasAttribute("tabindex")||(t.tabIndex=0))}),r}(t,(t,e)=>(nx(this.node,e,"Start"),(t,{success:e})=>nx(this.node,t,e?"End":"Cancel")),{useGlobalTarget:this.node.props.globalTapTarget}))}unmount(){}}let nw=new WeakMap,nb=new WeakMap,nP=t=>{let e=nw.get(t.target);e&&e(t)},nA=t=>{t.forEach(nP)},nS={some:0,all:1};class nM extends ih{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:t={}}=this.node.getProps(),{root:e,margin:i,amount:s="some",once:n}=t,r={root:e?e.current:void 0,rootMargin:i,threshold:"number"==typeof s?s:nS[s]};return function(t,e,i){let s=function({root:t,...e}){let i=t||document;nb.has(i)||nb.set(i,{});let s=nb.get(i),n=JSON.stringify(e);return s[n]||(s[n]=new IntersectionObserver(nA,{root:t,...e})),s[n]}(e);return nw.set(t,i),s.observe(t),()=>{nw.delete(t),s.unobserve(t)}}(this.node.current,r,t=>{let{isIntersecting:e}=t;if(this.isInView===e||(this.isInView=e,n&&!e&&this.hasEnteredView))return;e&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",e);let{onViewportEnter:i,onViewportLeave:s}=this.node.getProps(),r=e?i:s;r&&r(t)})}mount(){this.startObserver()}update(){if("undefined"==typeof IntersectionObserver)return;let{props:t,prevProps:e}=this.node;["amount","margin","root"].some(function({viewport:t={}},{viewport:e={}}={}){return i=>t[i]!==e[i]}(t,e))&&this.startObserver()}unmount(){}}let nV=(0,se.createContext)({strict:!1}),nk=(0,se.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),nE=(0,se.createContext)({});function nD(t){return n(t.animate)||ii.some(e=>it(t[e]))}function nC(t){return!!(nD(t)||t.variants)}function nR(t){return Array.isArray(t)?t.join(" "):t}let nj="undefined"!=typeof window,nL={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},nF={};for(let t in nL)nF[t]={isEnabled:e=>nL[t].some(t=>!!e[t])};let nB=Symbol.for("motionComponentSymbol"),nO=nj?se.useLayoutEffect:se.useEffect;function nI(t,{layout:e,layoutId:i}){return x.has(t)||t.startsWith("origin")||(e||void 0!==i)&&(!!sl[t]||"opacity"===t)}let nU=(t,e)=>e&&"number"==typeof t?e.transform(t):t,nN={...K,transform:Math.round},n$={borderWidth:th,borderTopWidth:th,borderRightWidth:th,borderBottomWidth:th,borderLeftWidth:th,borderRadius:th,radius:th,borderTopLeftRadius:th,borderTopRightRadius:th,borderBottomRightRadius:th,borderBottomLeftRadius:th,width:th,maxWidth:th,height:th,maxHeight:th,top:th,right:th,bottom:th,left:th,padding:th,paddingTop:th,paddingRight:th,paddingBottom:th,paddingLeft:th,margin:th,marginTop:th,marginRight:th,marginBottom:th,marginLeft:th,backgroundPositionX:th,backgroundPositionY:th,rotate:to,rotateX:to,rotateY:to,rotateZ:to,scale:G,scaleX:G,scaleY:G,scaleZ:G,skew:to,skewX:to,skewY:to,distance:th,translateX:th,translateY:th,translateZ:th,x:th,y:th,z:th,perspective:th,transformPerspective:th,opacity:_,originX:tc,originY:tc,originZ:th,zIndex:nN,fillOpacity:_,strokeOpacity:_,numOctaves:nN},nW={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},nz=v.length;function nY(t,e,i){let{style:s,vars:n,transformOrigin:r}=t,a=!1,o=!1;for(let t in e){let i=e[t];if(x.has(t)){a=!0;continue}if(Y(t)){n[t]=i;continue}{let e=nU(i,n$[t]);t.startsWith("origin")?(o=!0,r[t]=e):s[t]=e}}if(!e.transform&&(a||i?s.transform=function(t,e,i){let s="",n=!0;for(let r=0;r<nz;r++){let a=v[r],o=t[a];if(void 0===o)continue;let l=!0;if(!(l="number"==typeof o?o===+!!a.startsWith("scale"):0===parseFloat(o))||i){let t=nU(o,n$[a]);if(!l){n=!1;let e=nW[a]||a;s+=`${e}(${t}) `}i&&(e[a]=t)}}return s=s.trim(),i?s=i(e,n?"":s):n&&(s="none"),s}(e,t.transform,i):s.transform&&(s.transform="none")),o){let{originX:t="50%",originY:e="50%",originZ:i=0}=r;s.transformOrigin=`${t} ${e} ${i}`}}let nH=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function nX(t,e,i){for(let s in e)C(e[s])||nI(s,i)||(t[s]=e[s])}let nq={offset:"stroke-dashoffset",array:"stroke-dasharray"},nK={offset:"strokeDashoffset",array:"strokeDasharray"};function n_(t,{attrX:e,attrY:i,attrScale:s,pathLength:n,pathSpacing:r=1,pathOffset:a=0,...o},l,h,u){if(nY(t,o,h),l){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:c}=t;d.transform&&(c.transform=d.transform,delete d.transform),(c.transform||d.transformOrigin)&&(c.transformOrigin=d.transformOrigin??"50% 50%",delete d.transformOrigin),c.transform&&(c.transformBox=u?.transformBox??"fill-box",delete d.transformBox),void 0!==e&&(d.x=e),void 0!==i&&(d.y=i),void 0!==s&&(d.scale=s),void 0!==n&&function(t,e,i=1,s=0,n=!0){t.pathLength=1;let r=n?nq:nK;t[r.offset]=th.transform(-s);let a=th.transform(e),o=th.transform(i);t[r.array]=`${a} ${o}`}(d,n,r,a,!1)}let nG=()=>({...nH(),attrs:{}}),nZ=t=>"string"==typeof t&&"svg"===t.toLowerCase(),nJ=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function nQ(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||nJ.has(t)}let n0=t=>!nQ(t);try{!function(t){"function"==typeof t&&(n0=e=>e.startsWith("on")?!nQ(e):t(e))}(require("@emotion/is-prop-valid").default)}catch{}let n1=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function n5(t){if("string"!=typeof t||t.includes("-"));else if(n1.indexOf(t)>-1||/[A-Z]/u.test(t))return!0;return!1}let n2=t=>(e,i)=>{let s=(0,se.useContext)(nE),r=(0,se.useContext)(si),o=()=>(function({scrapeMotionValuesFromProps:t,createRenderState:e},i,s,r){return{latestValues:function(t,e,i,s){let r={},o=s(t,{});for(let t in o)r[t]=sf(o[t]);let{initial:l,animate:h}=t,u=nD(t),d=nC(t);e&&d&&!u&&!1!==t.inherit&&(void 0===l&&(l=e.initial),void 0===h&&(h=e.animate));let c=!!i&&!1===i.initial,p=(c=c||!1===l)?h:l;if(p&&"boolean"!=typeof p&&!n(p)){let e=Array.isArray(p)?p:[p];for(let i=0;i<e.length;i++){let s=a(t,e[i]);if(s){let{transitionEnd:t,transition:e,...i}=s;for(let t in i){let e=i[t];if(Array.isArray(e)){let t=c?e.length-1:0;e=e[t]}null!==e&&(r[t]=e)}for(let e in t)r[e]=t[e]}}}return r}(i,s,r,t),renderState:e()}})(t,e,s,r);return i?o():function(t){let e=(0,se.useRef)(null);return null===e.current&&(e.current=t()),e.current}(o)};function n3(t,e,i){let{style:s}=t,n={};for(let r in s)(C(s[r])||e.style&&C(e.style[r])||nI(r,t)||i?.getValue(r)?.liveStyle!==void 0)&&(n[r]=s[r]);return n}let n4={useVisualState:n2({scrapeMotionValuesFromProps:n3,createRenderState:nH})};function n9(t,e,i){let s=n3(t,e,i);for(let i in t)(C(t[i])||C(e[i]))&&(s[-1!==v.indexOf(i)?"attr"+i.charAt(0).toUpperCase()+i.substring(1):i]=t[i]);return s}let n6={useVisualState:n2({scrapeMotionValuesFromProps:n9,createRenderState:nG})},n8=t=>e=>e.test(t),n7=[K,th,tl,to,td,tu,{test:t=>"auto"===t,parse:t=>t}],rt=t=>n7.find(n8(t)),re=t=>/^-?(?:\d+(?:\.\d+)?|\.\d+)$/u.test(t),ri=/^var\(--(?:([\w-]+)|([\w-]+), ?([a-zA-Z\d ()%#.,-]+))\)/u,rs=t=>/^0[^.\s]+$/u.test(t),rn=new Set(["brightness","contrast","saturate","opacity"]);function rr(t){let[e,i]=t.slice(0,-1).split("(");if("drop-shadow"===e)return t;let[s]=i.match(J)||[];if(!s)return t;let n=i.replace(s,""),r=+!!rn.has(e);return s!==i&&(r*=100),e+"("+r+n+")"}let ra=/\b([a-z-]*)\(.*?\)/gu,ro={...tP,getAnimatableNone:t=>{let e=t.match(ra);return e?e.map(rr).join(" "):t}},rl={...n$,color:tm,backgroundColor:tm,outlineColor:tm,fill:tm,stroke:tm,borderColor:tm,borderTopColor:tm,borderRightColor:tm,borderBottomColor:tm,borderLeftColor:tm,filter:ro,WebkitFilter:ro},rh=t=>rl[t];function ru(t,e){let i=rh(t);return i!==ro&&(i=tP),i.getAnimatableNone?i.getAnimatableNone(e):void 0}let rd=new Set(["auto","none","0"]);class rc extends eF{constructor(t,e,i,s,n){super(t,e,i,s,n,!0)}readKeyframes(){let{unresolvedKeyframes:t,element:e,name:i}=this;if(!e||!e.current)return;super.readKeyframes();for(let i=0;i<t.length;i++){let s=t[i];if("string"==typeof s&&X(s=s.trim())){let n=function t(e,i,s=1){W(s<=4,`Max CSS variable fallback depth detected in property "${e}". This may indicate a circular fallback dependency.`);let[n,r]=function(t){let e=ri.exec(t);if(!e)return[,];let[,i,s,n]=e;return[`--${i??s}`,n]}(e);if(!n)return;let a=window.getComputedStyle(i).getPropertyValue(n);if(a){let t=a.trim();return re(t)?parseFloat(t):t}return X(r)?t(r,i,s+1):r}(s,e.current);void 0!==n&&(t[i]=n),i===t.length-1&&(this.finalKeyframe=s)}}if(this.resolveNoneKeyframes(),!T.has(i)||2!==t.length)return;let[s,n]=t,r=rt(s),a=rt(n);if(r!==a)if(eS(r)&&eS(a))for(let e=0;e<t.length;e++){let i=t[e];"string"==typeof i&&(t[e]=parseFloat(i))}else ek[i]&&(this.needsMeasurement=!0)}resolveNoneKeyframes(){let{unresolvedKeyframes:t,name:e}=this,i=[];for(let e=0;e<t.length;e++){var s;(null===t[e]||("number"==typeof(s=t[e])?0===s:null===s||"none"===s||"0"===s||rs(s)))&&i.push(e)}i.length&&function(t,e,i){let s,n=0;for(;n<t.length&&!s;){let e=t[n];"string"==typeof e&&!rd.has(e)&&tx(e).values.length&&(s=t[n]),n++}if(s&&i)for(let n of e)t[n]=ru(i,s)}(t,i,e)}measureInitialState(){let{element:t,unresolvedKeyframes:e,name:i}=this;if(!t||!t.current)return;"height"===i&&(this.suspendedScrollY=window.pageYOffset),this.measuredOrigin=ek[i](t.measureViewportBox(),window.getComputedStyle(t.current)),e[0]=this.measuredOrigin;let s=e[e.length-1];void 0!==s&&t.getValue(i,s).jump(s,!1)}measureEndState(){let{element:t,name:e,unresolvedKeyframes:i}=this;if(!t||!t.current)return;let s=t.getValue(e);s&&s.jump(this.measuredOrigin,!1);let n=i.length-1,r=i[n];i[n]=ek[e](t.measureViewportBox(),window.getComputedStyle(t.current)),null!==r&&void 0===this.finalKeyframe&&(this.finalKeyframe=r),this.removedTransforms?.length&&this.removedTransforms.forEach(([e,i])=>{t.getValue(e).set(i)}),this.resolveNoneKeyframes()}}let rp=[...n7,tm,tP],rm=t=>rp.find(n8(t)),rf={current:null},ry={current:!1},rg=new WeakMap,rv=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"];class rx{scrapeMotionValuesFromProps(t,e,i){return{}}constructor({parent:t,props:e,presenceContext:i,reducedMotionConfig:s,blockInitialAnimation:n,visualState:r},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.KeyframeResolver=eF,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.renderScheduledAt=0,this.scheduleRender=()=>{let t=S.now();this.renderScheduledAt<t&&(this.renderScheduledAt=t,m.render(this.render,!1,!0))};let{latestValues:o,renderState:l}=r;this.latestValues=o,this.baseTarget={...o},this.initialValues=e.initial?{...o}:{},this.renderState=l,this.parent=t,this.props=e,this.presenceContext=i,this.depth=t?t.depth+1:0,this.reducedMotionConfig=s,this.options=a,this.blockInitialAnimation=!!n,this.isControllingVariants=nD(e),this.isVariantNode=nC(e),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);let{willChange:h,...u}=this.scrapeMotionValuesFromProps(e,{},this);for(let t in u){let e=u[t];void 0!==o[t]&&C(e)&&e.set(o[t],!1)}}mount(t){this.current=t,rg.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((t,e)=>this.bindToMotionValue(e,t)),ry.current||function(){if(ry.current=!0,nj)if(window.matchMedia){let t=window.matchMedia("(prefers-reduced-motion)"),e=()=>rf.current=t.matches;t.addListener(e),e()}else rf.current=!1}(),this.shouldReduceMotion="never"!==this.reducedMotionConfig&&("always"===this.reducedMotionConfig||rf.current),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){for(let t in this.projection&&this.projection.unmount(),f(this.notifyUpdate),f(this.render),this.valueSubscriptions.forEach(t=>t()),this.valueSubscriptions.clear(),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this),this.events)this.events[t].clear();for(let t in this.features){let e=this.features[t];e&&(e.unmount(),e.isMounted=!1)}this.current=null}bindToMotionValue(t,e){let i;this.valueSubscriptions.has(t)&&this.valueSubscriptions.get(t)();let s=x.has(t);s&&this.onBindTransform&&this.onBindTransform();let n=e.on("change",e=>{this.latestValues[t]=e,this.props.onUpdate&&m.preRender(this.notifyUpdate),s&&this.projection&&(this.projection.isTransformDirty=!0)}),r=e.on("renderRequest",this.scheduleRender);window.MotionCheckAppearSync&&(i=window.MotionCheckAppearSync(this,t,e)),this.valueSubscriptions.set(t,()=>{n(),r(),i&&i(),e.owner&&e.stop()})}sortNodePosition(t){return this.current&&this.sortInstanceNodePosition&&this.type===t.type?this.sortInstanceNodePosition(this.current,t.current):0}updateFeatures(){let t="animation";for(t in nF){let e=nF[t];if(!e)continue;let{isEnabled:i,Feature:s}=e;if(!this.features[t]&&s&&i(this.props)&&(this.features[t]=new s(this)),this.features[t]){let e=this.features[t];e.isMounted?e.update():(e.mount(),e.isMounted=!0)}}}triggerBuild(){this.build(this.renderState,this.latestValues,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):iD()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,e){this.latestValues[t]=e}update(t,e){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=e;for(let e=0;e<rv.length;e++){let i=rv[e];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);let s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=function(t,e,i){for(let s in e){let n=e[s],r=i[s];if(C(n))t.addValue(s,n);else if(C(r))t.addValue(s,E(n,{owner:t}));else if(r!==n)if(t.hasValue(s)){let e=t.getValue(s);!0===e.liveStyle?e.jump(n):e.hasAnimated||e.set(n)}else{let e=t.getStaticValue(s);t.addValue(s,E(void 0!==e?e:n,{owner:t}))}}for(let s in i)void 0===e[s]&&t.removeValue(s);return e}(this,this.scrapeMotionValuesFromProps(t,this.prevProps,this),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}addVariantChild(t){let e=this.getClosestVariantNode();if(e)return e.variantChildren&&e.variantChildren.add(t),()=>e.variantChildren.delete(t)}addValue(t,e){let i=this.values.get(t);e!==i&&(i&&this.removeValue(t),this.bindToMotionValue(t,e),this.values.set(t,e),this.latestValues[t]=e.get())}removeValue(t){this.values.delete(t);let e=this.valueSubscriptions.get(t);e&&(e(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,e){if(this.props.values&&this.props.values[t])return this.props.values[t];let i=this.values.get(t);return void 0===i&&void 0!==e&&(i=E(null===e?void 0:e,{owner:this}),this.addValue(t,i)),i}readValue(t,e){let i=void 0===this.latestValues[t]&&this.current?this.getBaseTargetFromProps(this.props,t)??this.readValueFromInstance(this.current,t,this.options):this.latestValues[t];return null!=i&&("string"==typeof i&&(re(i)||rs(i))?i=parseFloat(i):!rm(i)&&tP.test(e)&&(i=ru(t,e)),this.setBaseTarget(t,C(i)?i.get():i)),C(i)?i.get():i}setBaseTarget(t,e){this.baseTarget[t]=e}getBaseTarget(t){let e,{initial:i}=this.props;if("string"==typeof i||"object"==typeof i){let s=a(this.props,i,this.presenceContext?.custom);s&&(e=s[t])}if(i&&void 0!==e)return e;let s=this.getBaseTargetFromProps(this.props,t);return void 0===s||C(s)?void 0!==this.initialValues[t]&&void 0===e?void 0:this.baseTarget[t]:s}on(t,e){return this.events[t]||(this.events[t]=new P),this.events[t].add(e)}notify(t,...e){this.events[t]&&this.events[t].notify(...e)}}class rT extends rx{constructor(){super(...arguments),this.KeyframeResolver=rc}sortInstanceNodePosition(t,e){return 2&t.compareDocumentPosition(e)?1:-1}getBaseTargetFromProps(t,e){return t.style?t.style[e]:void 0}removeValueFromRenderState(t,{vars:e,style:i}){delete e[t],delete i[t]}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:t}=this.props;C(t)&&(this.childSubscription=t.on("change",t=>{this.current&&(this.current.textContent=`${t}`)}))}}function rw(t,{style:e,vars:i},s,n){for(let r in Object.assign(t.style,e,n&&n.getProjectionStyles(s)),i)t.style.setProperty(r,i[r])}class rb extends rT{constructor(){super(...arguments),this.type="html",this.renderInstance=rw}readValueFromInstance(t,e){if(x.has(e))return this.projection?.isProjecting?ew(e):eP(t,e);{let i=window.getComputedStyle(t),s=(Y(e)?i.getPropertyValue(e):i[e])||0;return"string"==typeof s?s.trim():s}}measureInstanceViewportBox(t,{transformPagePoint:e}){return iW(t,e)}build(t,e,i){nY(t,e,i.transformTemplate)}scrapeMotionValuesFromProps(t,e,i){return n3(t,e,i)}}let rP=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);class rA extends rT{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1,this.measureInstanceViewportBox=iD}getBaseTargetFromProps(t,e){return t[e]}readValueFromInstance(t,e){if(x.has(e)){let t=rh(e);return t&&t.default||0}return e=rP.has(e)?e:j(e),t.getAttribute(e)}scrapeMotionValuesFromProps(t,e,i){return n9(t,e,i)}build(t,e,i){n_(t,e,this.isSVGTag,i.transformTemplate,i.style)}renderInstance(t,e,i,s){for(let i in rw(t,e,void 0,s),e.attrs)t.setAttribute(rP.has(i)?i:j(i),e.attrs[i])}mount(t){this.isSVGTag=nZ(t.tagName),super.mount(t)}}let rS=function(t){if("undefined"==typeof Proxy)return t;let e=new Map;return new Proxy((...e)=>t(...e),{get:(i,s)=>"create"===s?t:(e.has(s)||e.set(s,t(s)),e.get(s))})}((i6={animation:{Feature:iu},exit:{Feature:ic},inView:{Feature:nM},tap:{Feature:nT},focus:{Feature:nd},hover:{Feature:nu},pan:{Feature:i9},drag:{Feature:i3,ProjectionNode:na,MeasureLayout:su},layout:{ProjectionNode:na,MeasureLayout:su}},i8=(t,e)=>n5(t)?new rA(e):new rb(e,{allowProjection:t!==se.Fragment}),function(t,{forwardMotionProps:e}={forwardMotionProps:!1}){return function(t){var e,i;let{preloadedFeatures:s,createVisualElement:n,useRender:r,useVisualState:a,Component:o}=t;function l(t,e){var i,s,l;let h,u={...(0,se.useContext)(nk),...t,layoutId:function(t){let{layoutId:e}=t,i=(0,se.useContext)(ss).id;return i&&void 0!==e?i+"-"+e:e}(t)},{isStatic:d}=u,c=function(t){let{initial:e,animate:i}=function(t,e){if(nD(t)){let{initial:e,animate:i}=t;return{initial:!1===e||it(e)?e:void 0,animate:it(i)?i:void 0}}return!1!==t.inherit?e:{}}(t,(0,se.useContext)(nE));return(0,se.useMemo)(()=>({initial:e,animate:i}),[nR(e),nR(i)])}(t),p=a(t,d);if(!d&&nj){s=0,l=0,(0,se.useContext)(nV).strict;let t=function(t){let{drag:e,layout:i}=nF;if(!e&&!i)return{};let s={...e,...i};return{MeasureLayout:(null==e?void 0:e.isEnabled(t))||(null==i?void 0:i.isEnabled(t))?s.MeasureLayout:void 0,ProjectionNode:s.ProjectionNode}}(u);h=t.MeasureLayout,c.visualElement=function(t,e,i,s,n){let{visualElement:r}=(0,se.useContext)(nE),a=(0,se.useContext)(nV),o=(0,se.useContext)(si),l=(0,se.useContext)(nk).reducedMotion,h=(0,se.useRef)(null);s=s||a.renderer,!h.current&&s&&(h.current=s(t,{visualState:e,parent:r,props:i,presenceContext:o,blockInitialAnimation:!!o&&!1===o.initial,reducedMotionConfig:l}));let u=h.current,d=(0,se.useContext)(sn);u&&!u.projection&&n&&("html"===u.type||"svg"===u.type)&&function(t,e,i,s){let{layoutId:n,layout:r,drag:a,dragConstraints:o,layoutScroll:l,layoutRoot:h,layoutCrossfade:u}=e;t.projection=new i(t.latestValues,e["data-framer-portal-id"]?void 0:function t(e){if(e)return!1!==e.options.allowProjection?e.projection:t(e.parent)}(t.parent)),t.projection.setOptions({layoutId:n,layout:r,alwaysMeasureLayout:!!a||o&&iY(o),visualElement:t,animationType:"string"==typeof r?r:"both",initialPromotionConfig:s,crossfade:u,layoutScroll:l,layoutRoot:h})}(h.current,i,n,d);let c=(0,se.useRef)(!1);(0,se.useInsertionEffect)(()=>{u&&c.current&&u.update(i,o)});let p=i[L],m=(0,se.useRef)(!!p&&!window.MotionHandoffIsComplete?.(p)&&window.MotionHasOptimisedAnimation?.(p));return nO(()=>{u&&(c.current=!0,window.MotionIsMounted=!0,u.updateFeatures(),st.render(u.render),m.current&&u.animationState&&u.animationState.animateChanges())}),(0,se.useEffect)(()=>{u&&(!m.current&&u.animationState&&u.animationState.animateChanges(),m.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(p)}),m.current=!1))}),u}(o,p,u,n,t.ProjectionNode)}return(0,i7.jsxs)(nE.Provider,{value:c,children:[h&&c.visualElement?(0,i7.jsx)(h,{visualElement:c.visualElement,...u}):null,r(o,t,(i=c.visualElement,(0,se.useCallback)(t=>{t&&p.onMount&&p.onMount(t),i&&(t?i.mount(t):i.unmount()),e&&("function"==typeof e?e(t):iY(e)&&(e.current=t))},[i])),p,d,c.visualElement)]})}s&&function(t){for(let e in t)nF[e]={...nF[e],...t[e]}}(s),l.displayName="motion.".concat("string"==typeof o?o:"create(".concat(null!=(i=null!=(e=o.displayName)?e:o.name)?i:"",")"));let h=(0,se.forwardRef)(l);return h[nB]=o,h}({...n5(t)?n6:n4,preloadedFeatures:i6,useRender:function(t=!1){return(e,i,s,{latestValues:n},r)=>{let a=(n5(e)?function(t,e,i,s){let n=(0,se.useMemo)(()=>{let i=nG();return n_(i,e,nZ(s),t.transformTemplate,t.style),{...i.attrs,style:{...i.style}}},[e]);if(t.style){let e={};nX(e,t.style,t),n.style={...e,...n.style}}return n}:function(t,e){let i={},s=function(t,e){let i=t.style||{},s={};return nX(s,i,t),Object.assign(s,function({transformTemplate:t},e){return(0,se.useMemo)(()=>{let i=nH();return nY(i,e,t),Object.assign({},i.vars,i.style)},[e])}(t,e)),s}(t,e);return t.drag&&!1!==t.dragListener&&(i.draggable=!1,s.userSelect=s.WebkitUserSelect=s.WebkitTouchCallout="none",s.touchAction=!0===t.drag?"none":`pan-${"x"===t.drag?"y":"x"}`),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(i.tabIndex=0),i.style=s,i})(i,n,r,e),o=function(t,e,i){let s={};for(let n in t)("values"!==n||"object"!=typeof t.values)&&(n0(n)||!0===i&&nQ(n)||!e&&!nQ(n)||t.draggable&&n.startsWith("onDrag"))&&(s[n]=t[n]);return s}(i,"string"==typeof e,t),l=e!==se.Fragment?{...o,...a,ref:s}:{},{children:h}=i,u=(0,se.useMemo)(()=>C(h)?h.get():h,[h]);return(0,se.createElement)(e,{...l,children:u})}}(e),createVisualElement:i8,Component:t})}))},59099:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]])},59964:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]])},71539:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},72894:(t,e,i)=>{i.d(e,{A:()=>s});let s=(0,i(19946).A)("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]])}}]);