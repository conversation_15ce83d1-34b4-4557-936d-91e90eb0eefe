'use client'

import { motion } from 'framer-motion'
import { CheckCircle, Circle, Rocket, Brain, Zap, Target } from 'lucide-react'

const timelineEvents = [
  {
    year: '2024',
    quarter: 'Q1',
    title: 'Company Founded',
    description: 'Metamorphic Labs LLC was established with a vision to democratize AI technology.',
    status: 'completed',
    icon: Rocket,
  },
  {
    year: '2024',
    quarter: 'Q2',
    title: 'First AI Platform',
    description: 'Launched our flagship AI Integration & Broker System, revolutionizing multi-model AI access.',
    status: 'completed',
    icon: Brain,
  },
  {
    year: '2024',
    quarter: 'Q3',
    title: 'Metamorphic Reactor Beta',
    description: 'Released beta version of our VS Code extension with multi-agent consensus system.',
    status: 'completed',
    icon: Zap,
  },
  {
    year: '2024',
    quarter: 'Q4',
    title: 'Enterprise Partnerships',
    description: 'Established strategic partnerships with Fortune 500 companies for AI transformation.',
    status: 'completed',
    icon: Target,
  },
  {
    year: '2025',
    quarter: 'Q1',
    title: 'SaaS Suite Launch',
    description: 'Launching our on-demand SaaS factory with revolutionary EFX revenue sharing model.',
    status: 'in-progress',
    icon: Rocket,
  },
  {
    year: '2025',
    quarter: 'Q2',
    title: 'Living Pipeline',
    description: 'Introducing self-optimizing CI/CD infrastructure with AI-powered anomaly detection.',
    status: 'planned',
    icon: Brain,
  },
  {
    year: '2025',
    quarter: 'Q3',
    title: 'Global Expansion',
    description: 'Expanding operations internationally with offices in Europe and Asia-Pacific.',
    status: 'planned',
    icon: Target,
  },
]

export function TimelineSection() {
  return (
    <section className="py-24 sm:py-32 bg-surface-alt/50">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight text-text-primary sm:text-4xl mb-6">
            Our Journey
          </h2>
          <p className="text-lg text-text-secondary max-w-3xl mx-auto">
            From a bold vision to transformative reality—discover the milestones that have shaped 
            Metamorphic Labs into a leader in AI innovation.
          </p>
        </motion.div>

        <div className="relative">
          {/* Timeline line */}
          <div className="absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-secondary via-tertiary to-accent opacity-30 lg:left-1/2 lg:transform lg:-translate-x-px" />

          <div className="space-y-12">
            {timelineEvents.map((event, index) => (
              <motion.div
                key={`${event.year}-${event.quarter}`}
                initial={{ opacity: 0, y: 20 }}
                whileInView={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: index * 0.1 }}
                viewport={{ once: true }}
                className={`relative flex items-center ${
                  index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'
                }`}
              >
                {/* Timeline dot */}
                <div className="absolute left-8 transform -translate-x-1/2 lg:left-1/2">
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                    event.status === 'completed'
                      ? 'bg-accent border-accent'
                      : event.status === 'in-progress'
                      ? 'bg-secondary border-secondary'
                      : 'bg-surface border-tertiary'
                  }`}>
                    {event.status === 'completed' ? (
                      <CheckCircle className="h-4 w-4 text-surface" />
                    ) : (
                      <Circle className="h-4 w-4 text-current" />
                    )}
                  </div>
                </div>

                {/* Content */}
                <div className={`flex-1 ${index % 2 === 0 ? 'lg:pr-8' : 'lg:pl-8'} pl-20 lg:pl-0`}>
                  <div className={`${index % 2 === 0 ? 'lg:text-right' : 'lg:text-left'}`}>
                    <div className="flex items-center gap-3 mb-2">
                      <div className={`flex h-10 w-10 items-center justify-center rounded-full ${
                        event.status === 'completed'
                          ? 'bg-accent/10 text-accent'
                          : event.status === 'in-progress'
                          ? 'bg-secondary/10 text-secondary'
                          : 'bg-tertiary/10 text-tertiary'
                      }`}>
                        <event.icon className="h-5 w-5" />
                      </div>
                      <div className="text-sm font-medium text-text-secondary">
                        {event.year} {event.quarter}
                      </div>
                    </div>
                    <h3 className="text-xl font-semibold text-text-primary mb-2">
                      {event.title}
                    </h3>
                    <p className="text-text-secondary">
                      {event.description}
                    </p>
                  </div>
                </div>

                {/* Spacer for alternating layout */}
                <div className="hidden lg:block flex-1" />
              </motion.div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}
