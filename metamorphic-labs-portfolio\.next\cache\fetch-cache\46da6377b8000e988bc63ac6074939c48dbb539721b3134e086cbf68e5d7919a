{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e3fd1cd41e02b-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?select=%2A&slug=eq.metamorphic-testing", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:46:00 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=CuKNY2kN3Ay4_pQMYk4GBsoB6KLawY6uoxqOydbnSiU-1750621560-*******-8jYqzGwXgaEPkmPxvAQeKXmPfUpGR2mpVF3pm1JimhmCKtQrsWEQhp8LedeaIeJ7BToQl3h0xoQ9LTy4VFwlSURFLqxI6xvjdWKjbZHnneg; path=/; expires=Sun, 22-Jun-25 20:16:00 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "eyJzbHVnIjoibWV0YW1vcnBoaWMtdGVzdGluZyIsInRpdGxlIjoiTWV0YW1vcnBoaWMgVGVzdGluZyBGcmFtZXdvcmsiLCJzdW1tYXJ5IjoiQSBtZXRhbW9ycGhpYyB0ZXN0aW5nIHN1aXRlIGVuc3VyaW5nIEFJIHN0YWJpbGl0eSB1bmRlciBwZXJ0dXJiZWQgaW5wdXRzLCB3aXRoIHJlYWwtdGltZSBNUi1iYXNlZCBkZWJ1Z2dpbmcgYW5kIHNlbGYtYWRhcHRpdmUgdmFsaWRhdGlvbiBsb29wcy4iLCJib2R5X21kIjpudWxsLCJzdGF0dXMiOiJDb25jZXB0IiwiaGVyb191cmwiOiIvaW1hZ2VzL3Byb2plY3RzL21ldGFtb3JwaGljLXRlc3RpbmcuanBnIiwidGFncyI6WyJNZXRhbW9ycGhpYyBUZXN0aW5nIiwgIk1MIFZhbGlkYXRpb24iLCAiU2VsZi1IZWFsaW5nIl0sImNyZWF0ZWRfYXQiOiIyMDI1LTA2LTIxVDIzOjE0OjQ2LjcyNDIyOCswMDowMCIsInVwZGF0ZWRfYXQiOiIyMDI1LTA2LTIxVDIzOjE0OjQ2LjcyNDIyOCswMDowMCJ9", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&slug=eq.metamorphic-testing"}, "revalidate": 31536000, "tags": []}