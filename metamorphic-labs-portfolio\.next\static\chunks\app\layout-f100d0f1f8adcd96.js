(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{21578:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6874,23)),Promise.resolve().then(s.t.bind(s,62093,23)),Promise.resolve().then(s.t.bind(s,27735,23)),Promise.resolve().then(s.t.bind(s,30347,23)),Promise.resolve().then(s.bind(s,46325)),Promise.resolve().then(s.bind(s,61483))},27735:e=>{e.exports={style:{fontFamily:"'Geist Mono', '<PERSON>eist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},30285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var a=s(95155);s(12115);var r=s(54624),n=s(74466),i=s(59434);let l=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:n,asChild:c=!1,...d}=e,o=c?r.DX:"button";return(0,a.jsx)(o,{"data-slot":"button",className:(0,i.cn)(l({variant:s,size:n,className:t})),...d})}},30347:()=>{},46325:(e,t,s)=>{"use strict";s.d(t,{Navigation:()=>f});var a=s(95155),r=s(6874),n=s.n(r),i=s(18999),l=s(12115),c=s(19946);let d=(0,c.A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),o=(0,c.A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),h=(0,c.A)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),m=(0,c.A)("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]);var x=s(30285),v=s(61483),u=s(59434);let g=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Expertise",href:"/expertise"},{name:"Projects",href:"/projects"},{name:"Contact",href:"/contact"}];function f(){let e=(0,i.usePathname)(),{theme:t,toggleTheme:s}=(0,v.D)(),[r,c]=(0,l.useState)(!1);return(0,a.jsxs)("header",{className:"sticky top-0 z-50 w-full border-b border-secondary/20 bg-surface/80 backdrop-blur-md",children:[(0,a.jsxs)("nav",{className:"mx-auto flex max-w-7xl items-center justify-between p-6 lg:px-8","aria-label":"Global",children:[(0,a.jsx)("div",{className:"flex lg:flex-1",children:(0,a.jsx)(n(),{href:"/",className:"-m-1.5 p-1.5",children:(0,a.jsx)("span",{className:"text-2xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent",children:"Metamorphic Labs"})})}),(0,a.jsx)("div",{className:"flex lg:hidden",children:(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:()=>c(!r),className:"text-text-primary",children:[(0,a.jsx)("span",{className:"sr-only",children:"Open main menu"}),r?(0,a.jsx)(d,{className:"h-6 w-6","aria-hidden":"true"}):(0,a.jsx)(o,{className:"h-6 w-6","aria-hidden":"true"})]})}),(0,a.jsx)("div",{className:"hidden lg:flex lg:gap-x-12",children:g.map(t=>(0,a.jsx)(n(),{href:t.href,className:(0,u.cn)("text-sm font-medium transition-colors hover:text-secondary",e===t.href?"text-secondary border-b-2 border-secondary":"text-text-primary hover:text-secondary"),children:t.name},t.name))}),(0,a.jsxs)("div",{className:"hidden lg:flex lg:flex-1 lg:justify-end lg:gap-x-4",children:[(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:s,className:"text-text-primary hover:text-secondary",children:[(0,a.jsx)("span",{className:"sr-only",children:"Toggle theme"}),"dark"===t?(0,a.jsx)(h,{className:"h-5 w-5"}):(0,a.jsx)(m,{className:"h-5 w-5"})]}),(0,a.jsx)(x.$,{asChild:!0,className:"bg-accent text-surface hover:bg-accent/90",children:(0,a.jsx)(n(),{href:"/contact",children:"Get Started"})})]})]}),r&&(0,a.jsxs)("div",{className:"lg:hidden",children:[(0,a.jsx)("div",{className:"fixed inset-0 z-50"}),(0,a.jsxs)("div",{className:"fixed inset-y-0 right-0 z-50 w-full overflow-y-auto bg-surface px-6 py-6 sm:max-w-sm sm:ring-1 sm:ring-secondary/20",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)(n(),{href:"/",className:"-m-1.5 p-1.5",onClick:()=>c(!1),children:(0,a.jsx)("span",{className:"text-xl font-bold bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent",children:"Metamorphic Labs"})}),(0,a.jsxs)(x.$,{variant:"ghost",size:"sm",onClick:()=>c(!1),className:"text-text-primary",children:[(0,a.jsx)("span",{className:"sr-only",children:"Close menu"}),(0,a.jsx)(d,{className:"h-6 w-6","aria-hidden":"true"})]})]}),(0,a.jsx)("div",{className:"mt-6 flow-root",children:(0,a.jsxs)("div",{className:"-my-6 divide-y divide-secondary/20",children:[(0,a.jsx)("div",{className:"space-y-2 py-6",children:g.map(t=>(0,a.jsx)(n(),{href:t.href,onClick:()=>c(!1),className:(0,u.cn)("-mx-3 block rounded-lg px-3 py-2 text-base font-semibold leading-7 transition-colors",e===t.href?"bg-secondary/10 text-secondary":"text-text-primary hover:bg-secondary/5 hover:text-secondary"),children:t.name},t.name))}),(0,a.jsxs)("div",{className:"py-6 space-y-4",children:[(0,a.jsx)(x.$,{variant:"ghost",onClick:s,className:"w-full justify-start text-text-primary hover:text-secondary",children:"dark"===t?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(h,{className:"mr-2 h-5 w-5"}),"Light Mode"]}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(m,{className:"mr-2 h-5 w-5"}),"Dark Mode"]})}),(0,a.jsx)(x.$,{asChild:!0,className:"w-full bg-accent text-surface hover:bg-accent/90",children:(0,a.jsx)(n(),{href:"/contact",onClick:()=>c(!1),children:"Get Started"})})]})]})})]})]})]})}},59434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(52596),r=s(39688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}},61483:(e,t,s)=>{"use strict";s.d(t,{D:()=>l,ThemeProvider:()=>i});var a=s(95155),r=s(12115);let n=(0,r.createContext)({theme:"dark",setTheme:()=>null,toggleTheme:()=>null});function i(e){let{children:t,defaultTheme:s="dark",storageKey:i="metamorphic-theme",...l}=e,[c,d]=(0,r.useState)(s);return(0,r.useEffect)(()=>{let e=window.document.documentElement;e.classList.remove("light","dark"),e.classList.add(c)},[c]),(0,r.useEffect)(()=>{let e=localStorage.getItem(i);e&&d(e)},[i]),(0,a.jsx)(n.Provider,{...l,value:{theme:c,setTheme:e=>{localStorage.setItem(i,e),d(e)},toggleTheme:()=>{let e="dark"===c?"light":"dark";localStorage.setItem(i,e),d(e)}},children:t})}let l=()=>{let e=(0,r.useContext)(n);if(void 0===e)throw Error("useTheme must be used within a ThemeProvider");return e}},62093:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}}},e=>{var t=t=>e(e.s=t);e.O(0,[360,874,497,441,684,358],()=>t(21578)),_N_E=e.O()}]);