{"name": "metamorphic-labs-portfolio", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "format": "prettier --write .", "format:check": "prettier --check ."}, "dependencies": {"@google/generative-ai": "^0.24.1", "@radix-ui/react-slot": "^1.2.3", "@supabase/supabase-js": "^2.50.0", "@tanstack/react-query": "^5.81.2", "@types/react-syntax-highlighter": "^15.5.13", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.522.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-markdown": "^10.1.0", "react-syntax-highlighter": "^15.6.1", "tailwind-merge": "^3.3.1", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9", "eslint-config-next": "15.3.4", "jsdom": "^26.1.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.4", "typescript": "^5", "vitest": "^3.2.4"}}