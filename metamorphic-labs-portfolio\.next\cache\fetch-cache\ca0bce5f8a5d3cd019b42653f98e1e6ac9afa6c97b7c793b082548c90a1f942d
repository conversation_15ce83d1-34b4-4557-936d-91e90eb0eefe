{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "953e3fd1b8a84cab-MSP", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/projects?select=%2A&slug=eq.living-pipeline", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Sun, 22 Jun 2025 19:46:00 GMT", "sb-gateway-version": "1", "sb-project-ref": "etqkmihipaiiodiwxqbl", "server": "cloudflare", "set-cookie": "__cf_bm=XzWI0aVT9OBWIL5d_jraxA4VIoVP89qJ16NVwuGQagE-1750621560-*******-D1HoB_RjbxGdaRGRejqcxgq4aHw_lEiufPU2nvU3cyHxB2A_YphvkDDM2_Er1wChraP_Y1.r_osAoYpYM4zjpgcT2jkBecCGi68Hcx.egbo; path=/; expires=Sun, 22-Jun-25 20:16:00 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "2"}, "body": "eyJzbHVnIjoibGl2aW5nLXBpcGVsaW5lIiwidGl0bGUiOiJMaXZpbmcgUGlwZWxpbmUgKEFJLU9wdGltaXplZCBDSS9DRCkiLCJzdW1tYXJ5IjoiQSBcImxpdmluZ1wiIGV2ZW50LWRyaXZlbiBDSS9DRCBzdGFjayB0aGF0IHVzZXMgTUwgdG8gc3BvdCBmbGFraW5lc3MsIG9wdGltaXplIGJ1aWxkIGNhY2hlcywgYW5kIGNob29zZSByb2xsb3V0IHN0cmF0ZWdpZXMgKGJsdWUtZ3JlZW4sIGNhbmFyeSkgb24gdGhlIGZseS4iLCJib2R5X21kIjpudWxsLCJzdGF0dXMiOiJDb25jZXB0IiwiaGVyb191cmwiOiIvaW1hZ2VzL3Byb2plY3RzL2xpdmluZy1waXBlbGluZS5qcGciLCJ0YWdzIjpbIkNJL0NEIiwgIkFub21hbHkgRGV0ZWN0aW9uIiwgIlNlbGYtT3B0aW1pemluZyJdLCJjcmVhdGVkX2F0IjoiMjAyNS0wNi0yMVQyMzoxNDo0Ni43MjQyMjgrMDA6MDAiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNi0yMVQyMzoxNDo0Ni43MjQyMjgrMDA6MDAifQ==", "status": 200, "url": "https://etqkmihipaiiodiwxqbl.supabase.co/rest/v1/projects?select=*&slug=eq.living-pipeline"}, "revalidate": 31536000, "tags": []}