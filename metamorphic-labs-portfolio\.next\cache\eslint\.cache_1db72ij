[{"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts": "6", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts": "7", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts": "12", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx": "16", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx": "17"}, {"size": 2350, "mtime": 1750618990956, "results": "18", "hashOfConfig": "19"}, {"size": 177, "mtime": 1750619049605, "results": "20", "hashOfConfig": "19"}, {"size": 1631, "mtime": 1750618027571, "results": "21", "hashOfConfig": "19"}, {"size": 2123, "mtime": 1750618027509, "results": "22", "hashOfConfig": "19"}, {"size": 1989, "mtime": 1750618027553, "results": "23", "hashOfConfig": "19"}, {"size": 166, "mtime": 1750617982530, "results": "24", "hashOfConfig": "19"}, {"size": 997, "mtime": 1750618230463, "results": "25", "hashOfConfig": "19"}, {"size": 3023, "mtime": 1750618935221, "results": "26", "hashOfConfig": "19"}, {"size": 5113, "mtime": 1750619079034, "results": "27", "hashOfConfig": "19"}, {"size": 6056, "mtime": 1750618918333, "results": "28", "hashOfConfig": "19"}, {"size": 1667, "mtime": 1750618896405, "results": "29", "hashOfConfig": "19"}, {"size": 1669, "mtime": 1750618860869, "results": "30", "hashOfConfig": "19"}, {"size": 1544, "mtime": 1750619294721, "results": "31", "hashOfConfig": "19"}, {"size": 5562, "mtime": 1750619349449, "results": "32", "hashOfConfig": "19"}, {"size": 3858, "mtime": 1750619370683, "results": "33", "hashOfConfig": "19"}, {"size": 4178, "mtime": 1750619313785, "results": "34", "hashOfConfig": "19"}, {"size": 1400, "mtime": 1750619325074, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ytk0ae", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 17, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx", ["87"], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx", ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101", "102", "103", "104"], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx", [], [], {"ruleId": "105", "severity": 2, "message": "106", "line": 137, "column": 18, "nodeType": "107", "messageId": "108", "suggestions": "109"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 14, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 14, "endColumn": 27, "suggestions": "114"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 19, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 19, "endColumn": 27, "suggestions": "115"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 24, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 24, "endColumn": 27, "suggestions": "116"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 29, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 29, "endColumn": 27, "suggestions": "117"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 34, "column": 23, "nodeType": "112", "messageId": "113", "endLine": 34, "endColumn": 26, "suggestions": "118"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 39, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 39, "endColumn": 27, "suggestions": "119"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 44, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 44, "endColumn": 27, "suggestions": "120"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 49, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 49, "endColumn": 27, "suggestions": "121"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 54, "column": 28, "nodeType": "112", "messageId": "113", "endLine": 54, "endColumn": 31, "suggestions": "122"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 59, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 59, "endColumn": 27, "suggestions": "123"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 64, "column": 55, "nodeType": "112", "messageId": "113", "endLine": 64, "endColumn": 58, "suggestions": "124"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 84, "column": 32, "nodeType": "112", "messageId": "113", "endLine": 84, "endColumn": 35, "suggestions": "125"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 89, "column": 29, "nodeType": "112", "messageId": "113", "endLine": 89, "endColumn": 32, "suggestions": "126"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 102, "column": 27, "nodeType": "112", "messageId": "113", "endLine": 102, "endColumn": 30, "suggestions": "127"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 109, "column": 27, "nodeType": "112", "messageId": "113", "endLine": 109, "endColumn": 30, "suggestions": "128"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 114, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 114, "endColumn": 27, "suggestions": "129"}, {"ruleId": "110", "severity": 2, "message": "111", "line": 119, "column": 24, "nodeType": "112", "messageId": "113", "endLine": 119, "endColumn": 27, "suggestions": "130"}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["131", "132", "133", "134"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["135", "136"], ["137", "138"], ["139", "140"], ["141", "142"], ["143", "144"], ["145", "146"], ["147", "148"], ["149", "150"], ["151", "152"], ["153", "154"], ["155", "156"], ["157", "158"], ["159", "160"], ["161", "162"], ["163", "164"], ["165", "166"], ["167", "168"], {"messageId": "169", "data": "170", "fix": "171", "desc": "172"}, {"messageId": "169", "data": "173", "fix": "174", "desc": "175"}, {"messageId": "169", "data": "176", "fix": "177", "desc": "178"}, {"messageId": "169", "data": "179", "fix": "180", "desc": "181"}, {"messageId": "182", "fix": "183", "desc": "184"}, {"messageId": "185", "fix": "186", "desc": "187"}, {"messageId": "182", "fix": "188", "desc": "184"}, {"messageId": "185", "fix": "189", "desc": "187"}, {"messageId": "182", "fix": "190", "desc": "184"}, {"messageId": "185", "fix": "191", "desc": "187"}, {"messageId": "182", "fix": "192", "desc": "184"}, {"messageId": "185", "fix": "193", "desc": "187"}, {"messageId": "182", "fix": "194", "desc": "184"}, {"messageId": "185", "fix": "195", "desc": "187"}, {"messageId": "182", "fix": "196", "desc": "184"}, {"messageId": "185", "fix": "197", "desc": "187"}, {"messageId": "182", "fix": "198", "desc": "184"}, {"messageId": "185", "fix": "199", "desc": "187"}, {"messageId": "182", "fix": "200", "desc": "184"}, {"messageId": "185", "fix": "201", "desc": "187"}, {"messageId": "182", "fix": "202", "desc": "184"}, {"messageId": "185", "fix": "203", "desc": "187"}, {"messageId": "182", "fix": "204", "desc": "184"}, {"messageId": "185", "fix": "205", "desc": "187"}, {"messageId": "182", "fix": "206", "desc": "184"}, {"messageId": "185", "fix": "207", "desc": "187"}, {"messageId": "182", "fix": "208", "desc": "184"}, {"messageId": "185", "fix": "209", "desc": "187"}, {"messageId": "182", "fix": "210", "desc": "184"}, {"messageId": "185", "fix": "211", "desc": "187"}, {"messageId": "182", "fix": "212", "desc": "184"}, {"messageId": "185", "fix": "213", "desc": "187"}, {"messageId": "182", "fix": "214", "desc": "184"}, {"messageId": "185", "fix": "215", "desc": "187"}, {"messageId": "182", "fix": "216", "desc": "184"}, {"messageId": "185", "fix": "217", "desc": "187"}, {"messageId": "182", "fix": "218", "desc": "184"}, {"messageId": "185", "fix": "219", "desc": "187"}, "replaceWithAlt", {"alt": "220"}, {"range": "221", "text": "222"}, "Replace with `&apos;`.", {"alt": "223"}, {"range": "224", "text": "225"}, "Replace with `&lsquo;`.", {"alt": "226"}, {"range": "227", "text": "228"}, "Replace with `&#39;`.", {"alt": "229"}, {"range": "230", "text": "231"}, "Replace with `&rsquo;`.", "suggestUnknown", {"range": "232", "text": "233"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "234", "text": "235"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "236", "text": "233"}, {"range": "237", "text": "235"}, {"range": "238", "text": "233"}, {"range": "239", "text": "235"}, {"range": "240", "text": "233"}, {"range": "241", "text": "235"}, {"range": "242", "text": "233"}, {"range": "243", "text": "235"}, {"range": "244", "text": "233"}, {"range": "245", "text": "235"}, {"range": "246", "text": "233"}, {"range": "247", "text": "235"}, {"range": "248", "text": "233"}, {"range": "249", "text": "235"}, {"range": "250", "text": "233"}, {"range": "251", "text": "235"}, {"range": "252", "text": "233"}, {"range": "253", "text": "235"}, {"range": "254", "text": "233"}, {"range": "255", "text": "235"}, {"range": "256", "text": "233"}, {"range": "257", "text": "235"}, {"range": "258", "text": "233"}, {"range": "259", "text": "235"}, {"range": "260", "text": "233"}, {"range": "261", "text": "235"}, {"range": "262", "text": "233"}, {"range": "263", "text": "235"}, {"range": "264", "text": "233"}, {"range": "265", "text": "235"}, {"range": "266", "text": "233"}, {"range": "267", "text": "235"}, "&apos;", [4695, 4866], "\n              Let&apos;s discuss how we can build something amazing together. \n              Our team is ready to transform your ideas into intelligent solutions.\n            ", "&lsquo;", [4695, 4866], "\n              Let&lsquo;s discuss how we can build something amazing together. \n              Our team is ready to transform your ideas into intelligent solutions.\n            ", "&#39;", [4695, 4866], "\n              Let&#39;s discuss how we can build something amazing together. \n              Our team is ready to transform your ideas into intelligent solutions.\n            ", "&rsquo;", [4695, 4866], "\n              Let&rsquo;s discuss how we can build something amazing together. \n              Our team is ready to transform your ideas into intelligent solutions.\n            ", [402, 405], "unknown", [402, 405], "never", [554, 557], [554, 557], [710, 713], [710, 713], [854, 857], [854, 857], [997, 1000], [997, 1000], [1124, 1127], [1124, 1127], [1275, 1278], [1275, 1278], [1429, 1432], [1429, 1432], [1537, 1540], [1537, 1540], [1671, 1674], [1671, 1674], [1821, 1824], [1821, 1824], [2437, 2440], [2437, 2440], [2617, 2620], [2617, 2620], [3048, 3051], [3048, 3051], [3264, 3267], [3264, 3267], [3379, 3382], [3379, 3382], [3554, 3557], [3554, 3557]]