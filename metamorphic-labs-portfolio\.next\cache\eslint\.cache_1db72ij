[{"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts": "6", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts": "7", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx": "8", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx": "9", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx": "10", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx": "11", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts": "12", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx": "13", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx": "14", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx": "15", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx": "16", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx": "17", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\about\\page.tsx": "18", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\about-hero.tsx": "19", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mission-section.tsx": "20", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\team-section.tsx": "21", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\timeline-section.tsx": "22"}, {"size": 2350, "mtime": 1750618990956, "results": "23", "hashOfConfig": "24"}, {"size": 177, "mtime": 1750619049605, "results": "25", "hashOfConfig": "24"}, {"size": 1631, "mtime": 1750618027571, "results": "26", "hashOfConfig": "24"}, {"size": 2123, "mtime": 1750618027509, "results": "27", "hashOfConfig": "24"}, {"size": 1989, "mtime": 1750618027553, "results": "28", "hashOfConfig": "24"}, {"size": 166, "mtime": 1750617982530, "results": "29", "hashOfConfig": "24"}, {"size": 997, "mtime": 1750618230463, "results": "30", "hashOfConfig": "24"}, {"size": 3023, "mtime": 1750618935221, "results": "31", "hashOfConfig": "24"}, {"size": 5113, "mtime": 1750619079034, "results": "32", "hashOfConfig": "24"}, {"size": 6056, "mtime": 1750618918333, "results": "33", "hashOfConfig": "24"}, {"size": 1667, "mtime": 1750618896405, "results": "34", "hashOfConfig": "24"}, {"size": 1669, "mtime": 1750618860869, "results": "35", "hashOfConfig": "24"}, {"size": 1544, "mtime": 1750619294721, "results": "36", "hashOfConfig": "24"}, {"size": 5625, "mtime": 1750619523144, "results": "37", "hashOfConfig": "24"}, {"size": 4133, "mtime": 1750621515539, "results": "38", "hashOfConfig": "24"}, {"size": 4178, "mtime": 1750619313785, "results": "39", "hashOfConfig": "24"}, {"size": 1400, "mtime": 1750619325074, "results": "40", "hashOfConfig": "24"}, {"size": 645, "mtime": 1750621731034, "results": "41", "hashOfConfig": "24"}, {"size": 3501, "mtime": 1750621608106, "results": "42", "hashOfConfig": "24"}, {"size": 4298, "mtime": 1750621627175, "results": "43", "hashOfConfig": "24"}, {"size": 6763, "mtime": 1750621680513, "results": "44", "hashOfConfig": "24"}, {"size": 5652, "mtime": 1750621654353, "results": "45", "hashOfConfig": "24"}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ytk0ae", {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\footer.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\hero-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\navigation.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\theme-provider.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\supabase.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\projects\\[slug]\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mdx-content.tsx", [], ["112"], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-grid.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\projects-loading.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\about\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\about-hero.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\mission-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\team-section.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\timeline-section.tsx", [], [], {"ruleId": "113", "severity": 2, "message": "114", "line": 13, "column": 23, "nodeType": "115", "messageId": "116", "endLine": 13, "endColumn": 26, "suggestions": "117", "suppressions": "118"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["119", "120"], ["121"], {"messageId": "122", "fix": "123", "desc": "124"}, {"messageId": "125", "fix": "126", "desc": "127"}, {"kind": "128", "justification": "129"}, "suggestUnknown", {"range": "130", "text": "131"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "132", "text": "133"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", "directive", "", [367, 370], "unknown", [367, 370], "never"]