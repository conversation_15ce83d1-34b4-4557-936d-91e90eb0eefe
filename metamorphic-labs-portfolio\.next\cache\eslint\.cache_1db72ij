[{"C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx": "1", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx": "2", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx": "3", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx": "4", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx": "5", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts": "6", "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts": "7"}, {"size": 689, "mtime": 1750617773096, "results": "8", "hashOfConfig": "9"}, {"size": 4086, "mtime": 1750617787260, "results": "10", "hashOfConfig": "9"}, {"size": 1631, "mtime": 1750618027571, "results": "11", "hashOfConfig": "9"}, {"size": 2123, "mtime": 1750618027509, "results": "12", "hashOfConfig": "9"}, {"size": 1989, "mtime": 1750618027553, "results": "13", "hashOfConfig": "9"}, {"size": 166, "mtime": 1750617982530, "results": "14", "hashOfConfig": "9"}, {"size": 997, "mtime": 1750618230463, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "suppressedMessages": "18", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1ytk0ae", {"filePath": "19", "messages": "20", "suppressedMessages": "21", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Desktop\\New folder (3)\\metamorphic-labs-portfolio\\src\\test\\setup.ts", [], []]