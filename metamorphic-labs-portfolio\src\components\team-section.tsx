'use client'

import { motion } from 'framer-motion'
import { <PERSON>, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Linkedin, Github, Twitter } from 'lucide-react'

const teamMembers = [
  {
    name: 'Dr. <PERSON>',
    role: 'Founder & CEO',
    bio: 'Former AI Research Director at Google DeepMind with 15+ years in machine learning and distributed systems. PhD in Computer Science from Stanford.',
    expertise: ['AI Strategy', 'Machine Learning', 'Product Vision'],
    social: {
      linkedin: 'https://linkedin.com/in/alexchen',
      github: 'https://github.com/alexchen',
      twitter: 'https://twitter.com/alexchen'
    }
  },
  {
    name: '<PERSON>',
    role: 'CTO & Co-Founder',
    bio: 'Previously Lead Engineer at OpenAI and Anthropic. Expert in large-scale AI infrastructure and model optimization. MS Computer Science from MIT.',
    expertise: ['AI Infrastructure', 'System Architecture', 'Model Optimization'],
    social: {
      linkedin: 'https://linkedin.com/in/sarahrodriguez',
      github: 'https://github.com/sarahrodriguez'
    }
  },
  {
    name: '<PERSON>',
    role: 'Head of Engineering',
    bio: 'Former Principal Engineer at Microsoft Azure AI. Specializes in cloud-native architectures and enterprise AI solutions. 12+ years in distributed systems.',
    expertise: ['Cloud Architecture', 'Enterprise AI', 'DevOps'],
    social: {
      linkedin: 'https://linkedin.com/in/marcusthompson',
      github: 'https://github.com/marcusthompson'
    }
  },
  {
    name: 'Dr. Priya Patel',
    role: 'Head of AI Research',
    bio: 'Former Research Scientist at Facebook AI Research (FAIR). PhD in Machine Learning from Carnegie Mellon. Published 50+ papers in top-tier conferences.',
    expertise: ['Deep Learning', 'NLP', 'Computer Vision'],
    social: {
      linkedin: 'https://linkedin.com/in/priyapatel',
      twitter: 'https://twitter.com/priyapatel'
    }
  }
]

export function TeamSection() {
  return (
    <section className="py-24 sm:py-32">
      <div className="mx-auto max-w-7xl px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-3xl font-bold tracking-tight text-text-primary sm:text-4xl mb-6">
            Meet Our Team
          </h2>
          <p className="text-lg text-text-secondary max-w-3xl mx-auto">
            Our diverse team of AI experts, engineers, and visionaries brings together decades of 
            experience from leading technology companies and research institutions.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-2">
          {teamMembers.map((member, index) => (
            <motion.div
              key={member.name}
              initial={{ opacity: 0, y: 20 }}
              whileInView={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              viewport={{ once: true }}
            >
              <Card className="bg-surface-alt border-secondary/20 h-full hover:border-secondary/40 transition-all duration-300">
                <CardContent className="p-8">
                  <div className="flex items-start space-x-4">
                    {/* Avatar placeholder */}
                    <div className="flex-shrink-0">
                      <div className="h-16 w-16 rounded-full bg-gradient-to-br from-secondary via-tertiary to-accent flex items-center justify-center">
                        <span className="text-xl font-bold text-white">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <h3 className="text-xl font-semibold text-text-primary mb-1">
                        {member.name}
                      </h3>
                      <p className="text-secondary font-medium mb-3">
                        {member.role}
                      </p>
                      <p className="text-text-secondary text-sm mb-4 leading-relaxed">
                        {member.bio}
                      </p>
                      
                      {/* Expertise badges */}
                      <div className="flex flex-wrap gap-2 mb-4">
                        {member.expertise.map((skill) => (
                          <Badge
                            key={skill}
                            variant="secondary"
                            className="text-xs bg-tertiary/10 text-tertiary border-tertiary/20"
                          >
                            {skill}
                          </Badge>
                        ))}
                      </div>
                      
                      {/* Social links */}
                      <div className="flex space-x-3">
                        {member.social.linkedin && (
                          <a
                            href={member.social.linkedin}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-text-secondary hover:text-secondary transition-colors"
                          >
                            <Linkedin className="h-5 w-5" />
                          </a>
                        )}
                        {member.social.github && (
                          <a
                            href={member.social.github}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-text-secondary hover:text-secondary transition-colors"
                          >
                            <Github className="h-5 w-5" />
                          </a>
                        )}
                        {member.social.twitter && (
                          <a
                            href={member.social.twitter}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-text-secondary hover:text-secondary transition-colors"
                          >
                            <Twitter className="h-5 w-5" />
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>
      </div>
    </section>
  )
}
