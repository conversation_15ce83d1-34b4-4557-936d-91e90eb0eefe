'use client'

import { motion } from 'framer-motion'
import { <PERSON>, Zap, Target } from 'lucide-react'

export function AboutHero() {
  return (
    <section className="relative py-24 sm:py-32 overflow-hidden">
      {/* Background gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/20 via-secondary/10 to-tertiary/20" />
      
      <div className="relative mx-auto max-w-7xl px-6 lg:px-8">
        <div className="mx-auto max-w-4xl text-center">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h1 className="text-4xl font-extrabold tracking-tight text-text-primary sm:text-6xl lg:text-7xl">
              About{' '}
              <span className="bg-gradient-to-r from-secondary via-tertiary to-accent bg-clip-text text-transparent">
                Metamorphic Labs
              </span>
            </h1>
          </motion.div>
          
          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="mt-6 text-lg leading-8 text-text-secondary sm:text-xl lg:text-2xl max-w-3xl mx-auto"
          >
            We are a forward-thinking technology company dedicated to transforming ideas into 
            intelligent solutions through adaptive AI technology and innovative software development.
          </motion.p>
          
          {/* Core Values */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="mt-16 grid grid-cols-1 gap-8 sm:grid-cols-3"
          >
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-secondary/10 mb-4">
                <Brain className="h-8 w-8 text-secondary" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Innovation</h3>
              <p className="text-text-secondary text-sm">
                Pushing the boundaries of what&apos;s possible with AI and emerging technologies.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-tertiary/10 mb-4">
                <Zap className="h-8 w-8 text-tertiary" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Excellence</h3>
              <p className="text-text-secondary text-sm">
                Delivering high-quality solutions that exceed expectations and drive results.
              </p>
            </div>
            
            <div className="flex flex-col items-center text-center">
              <div className="flex h-16 w-16 items-center justify-center rounded-full bg-accent/10 mb-4">
                <Target className="h-8 w-8 text-accent" />
              </div>
              <h3 className="text-lg font-semibold text-text-primary mb-2">Impact</h3>
              <p className="text-text-secondary text-sm">
                Creating meaningful change that transforms businesses and improves lives.
              </p>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
